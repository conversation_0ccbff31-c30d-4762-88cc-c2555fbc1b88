import { Component } from '@angular/core';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';
import { LanguageService } from "../../core/services/language.service";

@Component({
  selector: 'app-language-toggle',
  template: `
    <button
      [matMenuTriggerFor]="menu"
      class="p-2 rounded-full hover:bg-tertiary focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
      aria-label="Change language"
      title="Change language"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="2" y1="12" x2="22" y2="12"></line>
        <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
      </svg>
    </button>

    <mat-menu #menu="matMenu" class="text-xs">
      <button
        mat-menu-item
        *ngFor="let lang of languages"
        (click)="changeLanguage(lang.code)"
        class="text-xs px-3 py-2"
      >
        {{ lang.label }}
      </button>
    </mat-menu>
  `,
  standalone: true,
  imports: [MatMenuModule, MatButtonModule, CommonModule]
})
export class LanguageToggleComponent {
  currentLanguage: string;
  languages = [
    { code: 'en', label: 'ENG' },
    { code: 'ru', label: 'RUS' },
    { code: 'kk', label: 'ҚАЗ' }
  ];

  constructor(private languageService: LanguageService) {
    this.currentLanguage = this.languageService.getCurrentLanguage();
  }

  changeLanguage(lang: string): void {
    this.languageService.changeLanguage(lang);
    this.currentLanguage = lang;
  }
}
