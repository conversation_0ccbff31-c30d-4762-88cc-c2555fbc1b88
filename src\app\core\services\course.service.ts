import {Injectable} from '@angular/core';
import {Observable, switchMap} from "rxjs";
import {HttpClient} from "@angular/common/http";
import {environments} from "../../environments/environments";
import {ThreadService} from "./thread.service";

@Injectable({
  providedIn: 'root'
})
export class CourseService {

  private apiUrl = environments.API


  constructor(private http: HttpClient,
              private threadService: ThreadService,) {

  }

  getAllCourses(): Observable<any> {
    return this.http.get(`${this.apiUrl}/course`);
  }

  getCourseByID(id: number | null) {
    return this.http.get(`${this.apiUrl}/course/${id}`);
  }


  getCourseInfoByThread(threadId: number) {
    return this.threadService.getThreadInfoByID(threadId).pipe(
      switchMap(thread => this.getCourseByID(thread.course_id)),
    );
  }




}
