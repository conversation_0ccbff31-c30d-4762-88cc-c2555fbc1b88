/* Score display styles */
.text-green-600 {
  font-weight: 500;
}

.text-yellow-600 {
  font-weight: 500;
}

.text-red-600 {
  font-weight: 500;
}

/* Badge styles */
.rounded-full {
  transition: all 0.2s ease-in-out;
}

.rounded-full:hover {
  transform: scale(1.05);
}

.bg-green-100 {
  border: 1px solid rgba(0, 128, 0, 0.1);
}

.bg-yellow-100 {
  border: 1px solid rgba(255, 193, 7, 0.1);
}

.bg-red-100 {
  border: 1px solid rgba(220, 53, 69, 0.1);
}

.bg-gray-100 {
  border: 1px solid rgba(108, 117, 125, 0.1);
}

.bg-blue-100 {
  border: 1px solid rgba(13, 110, 253, 0.1);
}

/* Hover effect for assignment items */
a.hover\:shadow:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Transition for color changes */
[class*="text-"] {
  transition: color 0.2s ease-in-out;
}

/* Search and filter section styles */
input:focus, select:focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Week header styles */
.week-header {
  transition: background-color 0.2s ease-in-out;
}

.week-header:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

/* Collapsible content animation */
.collapsible-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.collapsible-content.expanded {
  max-height: 2000px; /* Arbitrary large value */
  transition: max-height 0.5s ease-in;
}

/* Icon animations */
.rotate-icon {
  transition: transform 0.3s ease;
}

.rotate-icon.expanded {
  transform: rotate(180deg);
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Assignment item styles */
.assignment-item {
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.assignment-item:hover {
  transform: translateY(-2px);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Status icons */
.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

/* Empty state styles */
.empty-state {
  opacity: 0.8;
  transition: opacity 0.2s ease-in-out;
}

.empty-state:hover {
  opacity: 1;
}

/* Deadline status styles */
.deadline-far {
  background-color: rgba(52, 152, 219, 0.1) !important;
  color: #3498db !important;
  border: 1px solid rgba(52, 152, 219, 0.2) !important;
}

.deadline-approaching {
  background-color: rgba(241, 196, 15, 0.1) !important;
  color: #f39c12 !important;
  border: 1px solid rgba(241, 196, 15, 0.2) !important;
}

.deadline-close {
  background-color: rgba(231, 76, 60, 0.1) !important;
  color: #e74c3c !important;
  border: 1px solid rgba(231, 76, 60, 0.2) !important;
}

.deadline-overdue {
  background-color: rgba(192, 57, 43, 0.1) !important;
  color: #c0392b !important;
  border: 1px solid rgba(192, 57, 43, 0.2) !important;
  font-weight: 500;
}

.deadline-text-far {
  color: #3498db !important;
}

.deadline-text-approaching {
  color: #f39c12 !important;
}

.deadline-text-close {
  color: #e74c3c !important;
}

.deadline-text-overdue {
  color: #c0392b !important;
  font-weight: 500;
}