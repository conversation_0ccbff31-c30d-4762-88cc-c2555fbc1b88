import {Component, Inject, OnDestroy, OnInit} from '@angular/core';
import {FormControl} from "@angular/forms";
import {MatSnackBar} from "@angular/material/snack-bar";
import {ThreadService} from "../../core/services/thread.service";
import {CourseService} from "../../core/services/course.service";
import {AuthService} from "../../core/services/auth.service";
import {timestampToDate} from "../../shared/utils/date.utils";
import {Router, NavigationStart, Event as RouterEvent} from "@angular/router";
import {interval, Subscription, filter} from "rxjs";
import {switchMap} from "rxjs/operators";

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent implements OnInit, OnDestroy {

  constructor(
    private snackBar: MatSnackBar,
    private threadService: ThreadService,
    private courseService: CourseService,
    private authService: AuthService,
    private router: Router
  ) {
  }

  user: any

  breadcrumbs: { label: string, url?: string }[] = [
    {label: 'Главная', url: '/'},
    {label: 'Регистрация на курс',},
  ];


  courses: any[] = [];
  threads: any[] = [];
  user_threads: any[] = []

  filteredThreads: any[] = [];
  courseControl = new FormControl();

  // Filter properties
  searchQuery: string = '';
  dayFilter: string = '';
  availabilityFilter: string = '';

  // Loading state variables
  isLoadingThreads: boolean = false;
  isLoadingCourses: boolean = false;
  isLoadingUserThreads: boolean = false;

  // Subscription for periodic updates
  private updateSubscription: Subscription | null = null;

  // Subscription for router events
  private routerSubscription: Subscription | null = null;

  // Update interval in milliseconds (2 seconds)
  private readonly UPDATE_INTERVAL = 4000;

  // Flag to track if component is active (visible to user)
  private isComponentActive = true;



  ngOnInit() {
    this.user = this.authService.getCurrentUser();

    // Check if user is a student
    if (this.user.role !== 'student') {
      this.snackBar.open('Доступ запрещен: Только студенты могут регистрироваться на курсы', 'Закрыть', {
        duration: 5000,
        panelClass: ['snackbar-error'],
      });
      this.router.navigate(['/']);
      return;
    }

    // Subscribe to router events to detect navigation away from this component
    this.routerSubscription = this.router.events
      .pipe(
        filter((event): event is NavigationStart => event instanceof NavigationStart)
      )
      .subscribe((event: NavigationStart) => {
        // If navigating to a different route (not just query param changes on the same route)
        if (!event.url.startsWith('/register')) {
          // Mark component as inactive and stop periodic updates
          this.isComponentActive = false;
          this.stopPeriodicUpdates();
        } else {
          // Mark component as active when navigating back to register
          this.isComponentActive = true;
        }
      });

    console.log(this.user)
    this.getAllThreads();
    this.getAllCourses();
    this.getUserThreads()

    this.courseControl.valueChanges.subscribe(() => {
      // Apply all filters when course changes
      this.applyFilters();

      // Restart periodic updates when filter changes (only if component is active)
      if (this.isComponentActive) {
        this.startPeriodicUpdates();
      }
    });
  }

  displayCourseTitle(course: any): string {
    return course?.title || '';
  }

  getAllThreads() {
    this.isLoadingThreads = true;
    this.threadService.getAllThreads().subscribe({
      next: data => {
        this.threads = data;

        // Apply any existing filters
        this.applyFilters();

        console.log(this.threads);

        // Start periodic updates after initial data is loaded (only if component is active)
        if (this.isComponentActive) {
          this.startPeriodicUpdates();
        }
        this.isLoadingThreads = false;
      },
      error: err => {
        console.error(err);
        this.isLoadingThreads = false;
      }
    });
  }

  /**
   * Start periodic updates of thread availability
   */
  startPeriodicUpdates() {
    // Cancel any existing subscription
    this.stopPeriodicUpdates();

    // Only start updates if we have threads and component is active
    if (this.threads.length === 0 || !this.isComponentActive) {
      return;
    }

    // Get all thread IDs
    const threadIds = this.threads.map(thread => thread.id);

    // Create a new subscription that fires every UPDATE_INTERVAL milliseconds
    this.updateSubscription = interval(this.UPDATE_INTERVAL)
      .pipe(
        // Convert the interval event to an API call
        switchMap(() => {
          // Double-check component is still active before making API call
          if (!this.isComponentActive) {
            this.stopPeriodicUpdates();
            return [];
          }
          return this.threadService.getThreadsAvailability(threadIds);
        })
      )
      .subscribe({
        next: (response: any) => {
          if (response && response.thread_availability) {
            this.updateThreadAvailability(response.thread_availability);
          }
        },
        error: err => {
          console.error('Error updating thread availability:', err);
        }
      });
  }

  /**
   * Stop periodic updates
   */
  stopPeriodicUpdates() {
    if (this.updateSubscription) {
      this.updateSubscription.unsubscribe();
      this.updateSubscription = null;
    }
  }

  /**
   * Update thread availability information
   * @param availabilityData Array of thread availability data
   */
  updateThreadAvailability(availabilityData: any[]) {
    // Update threads array
    availabilityData.forEach(availItem => {
      // Update in threads array
      const threadIndex = this.threads.findIndex(t => t.id === availItem.id);
      if (threadIndex !== -1) {
        this.threads[threadIndex].max_students = availItem.max_students;
        this.threads[threadIndex].booked_slots = availItem.booked_slots;
        this.threads[threadIndex].available_slots = availItem.available_slots;
      }
    });

    // Re-apply filters to update filteredThreads with new availability data
    this.applyFilters();
  }

  getUserThreads() {
    this.isLoadingUserThreads = true;
    this.threadService.getThreadsWithScheduleForUser(this.user.id).subscribe({
      next: (data: any) => {
        this.user_threads = data.threads.map((item: any) => {
          // Extract semester for date conversion
          const semester = item.semester;

          return {
            ...item,
            semester: {
              ...semester,
              start_date: timestampToDate(semester.start_date),
              end_date: timestampToDate(semester.end_date),
            }
          };
        });

        console.log('User threads with schedules:', this.user_threads);
        this.isLoadingUserThreads = false;
      },
      error: err => {
        console.log('Error fetching user threads with schedules:', err);
        // Fallback to the old endpoint if the new one fails
        this.threadService.getListOfThreadsForUser(this.user.id).subscribe({
          next: (data: any) => {
            this.user_threads = data.threads.map((item: any) => {
              // Extract semester for date conversion
              const semester = item.semester;

              return {
                ...item,
                semester: {
                  ...semester,
                  start_date: timestampToDate(semester.start_date),
                  end_date: timestampToDate(semester.end_date),
                }
              };
            });

            console.log(this.user_threads);
            this.isLoadingUserThreads = false;
          },
          error: fallbackErr => {
            console.log(fallbackErr);
            this.isLoadingUserThreads = false;
          }
        });
      }
    });
  }

  getAllCourses() {
    this.isLoadingCourses = true;
    this.courseService.getAllCourses().subscribe({
      next: data => {
        this.courses = data.courses;
        this.isLoadingCourses = false;
      },
      error: err => {
        console.error(err);
        this.isLoadingCourses = false;
      }
    });
  }


  /**
   * Check if a thread has schedule conflicts with user's registered threads
   * @param thread_id ID of the thread to check
   * @returns Object with conflict information
   */
  hasScheduleConflict(thread_id: number): { hasConflict: boolean; conflictMessage: string } {
    const threadToCheck = this.threads.find(t => t.id === thread_id);
    if (!threadToCheck) {
      return { hasConflict: false, conflictMessage: '' };
    }
    return this.checkScheduleConflicts(threadToCheck);
  }

  registerToThread(thread_id: number) {
    // Find the thread the user wants to register for
    const threadToRegister = this.threads.find(t => t.id === thread_id);

    if (!threadToRegister) {
      this.snackBar.open('Поток не найден', 'Закрыть', {duration: 3000});
      return;
    }

    // Check if there are available seats
    if (threadToRegister.available_slots <= 0) {
      this.snackBar.open(
        'Нет доступных мест в этом потоке',
        'Закрыть',
        {duration: 3000, panelClass: ['snackbar-error']}
      );
      return;
    }

    // Check for schedule conflicts
    const conflictInfo = this.checkScheduleConflicts(threadToRegister);

    if (conflictInfo.hasConflict) {
      this.snackBar.open(
        `Конфликт расписания: ${conflictInfo.conflictMessage}`,
        'Закрыть',
        {duration: 6000, panelClass: ['snackbar-error']}
      );
      return;
    }

    this.threadService.registerOnThread(this.user.id, thread_id).subscribe({
      next: (res: any) => {
        if ('message' in res) {
          this.snackBar.open(res.message, 'Закрыть', {duration: 6000});
          this.getUserThreads();

          // Restart periodic updates to reflect the new registration (only if component is active)
          if (this.isComponentActive) {
            this.startPeriodicUpdates();
          }
        } else if ('error' in res) {
          this.snackBar.open('Вы уже зарегистрированы на этот поток', 'Закрыть', {duration: 3000});
        }
      },
      error: err => {
        this.snackBar.open('Ошибка при регистрации', 'Закрыть', {duration: 3000});
        console.error(err);
      }
    });
  }

  unregisterFromThread(thread_id: number) {
    this.threadService.removeFromThread(this.user.id, thread_id).subscribe({
      next: (res: any) => {
        if ('message' in res) {
          this.snackBar.open(res.message, 'Закрыть', {duration: 6000});
          this.getUserThreads();

          // Restart periodic updates to reflect the registration cancellation (only if component is active)
          if (this.isComponentActive) {
            this.startPeriodicUpdates();
          }
        } else if ('error' in res) {
          this.snackBar.open('Вы не зарегистрированы на этот поток', 'Закрыть', {duration: 3000});
        }
      },
      error: err => {
        this.snackBar.open('Ошибка при отмене регистрации', 'Закрыть', {duration: 3000});
        console.error(err);
      }
    });
  }



  /**
   * Apply all filters to the threads list
   */
  applyFilters() {
    // Start with all threads or filtered by course if a course is selected
    const courseId = this.courseControl.value ? this.courseControl.value.id : null;
    let filtered = courseId
      ? this.threads.filter(thread => thread.course_id === courseId)
      : this.threads;

    // Apply search filter if search query exists
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      filtered = filtered.filter(thread =>
        thread.course.title.toLowerCase().includes(query) ||
        (thread.teacher.name + ' ' + thread.teacher.surname).toLowerCase().includes(query)
      );
    }

    // Apply day of week filter if selected
    if (this.dayFilter) {
      filtered = filtered.filter(thread =>
        thread.schedules && thread.schedules.some((schedule: any) =>
          schedule.day_of_week.toString() === this.dayFilter
        )
      );
    }

    // Apply availability filter if selected
    if (this.availabilityFilter === 'available') {
      filtered = filtered.filter(thread => thread.available_slots > 0);
    } else if (this.availabilityFilter === 'full') {
      filtered = filtered.filter(thread => thread.available_slots <= 0);
    }

    this.filteredThreads = filtered;
  }

  /**
   * Reset all filters
   */
  resetFilters() {
    this.courseControl.setValue(null);
    this.searchQuery = '';
    this.dayFilter = '';
    this.availabilityFilter = '';
    this.filteredThreads = this.threads;

    // Restart periodic updates when filters are reset (only if component is active)
    if (this.isComponentActive) {
      this.startPeriodicUpdates();
    }
  }

  /**
   * Calculate the percentage of available seats
   * @param thread The thread to calculate availability for
   * @returns Percentage of available seats (0-100)
   */
  getAvailabilityPercentage(thread: any): number {
    if (!thread || !thread.max_students || thread.max_students === 0) {
      return 0;
    }
    return (thread.available_slots / thread.max_students) * 100;
  }

  isUserRegistered(thread_id: number): boolean {
    return this.user_threads.some(ut => ut.thread.id === thread_id);
  }

  /**
   * Format day of week number to readable text
   * @param dayOfWeek Day of week number (1-7, where 1 is Monday)
   * @returns Formatted day name
   */
  formatDayOfWeek(dayOfWeek: number): string {
    const days = [
      'Пн', // Monday (1)
      'Вт', // Tuesday (2)
      'Ср', // Wednesday (3)
      'Чт', // Thursday (4)
      'Пт', // Friday (5)
      'Сб', // Saturday (6)
      'Вс'  // Sunday (7)
    ];

    // Adjust index (dayOfWeek is 1-based, array is 0-based)
    const index = dayOfWeek - 1;

    // Return day name if valid, otherwise return unknown
    return index >= 0 && index < days.length ? days[index] : 'Неизвестно';
  }

  /**
   * Format time string to remove seconds
   * @param timeString Time string in format HH:MM:SS
   * @returns Formatted time string in format HH:MM
   */
  formatTime(timeString: string): string {
    if (!timeString) return '';

    // Split the time string by colon and take only hours and minutes
    const parts = timeString.split(':');
    if (parts.length >= 2) {
      return `${parts[0]}:${parts[1]}`;
    }

    // Return original string if it doesn't match expected format
    return timeString;
  }

  /**
   * Check if there are schedule conflicts between a thread and the user's registered threads
   * @param thread The thread to check for conflicts
   * @returns Object with conflict information
   */
  checkScheduleConflicts(thread: any): { hasConflict: boolean; conflictMessage: string } {
    // Default result - no conflicts
    const result = { hasConflict: false, conflictMessage: '' };

    // If the thread has no schedules, we can't check for conflicts
    if (!thread.schedules || thread.schedules.length === 0) {
      return result;
    }

    // Get all user's registered threads
    for (const userThread of this.user_threads) {
      // Get the schedules from the thread object
      // The API might return schedules directly in the thread object or nested in thread.schedules
      const userThreadSchedules = userThread.schedules ||
                                 (userThread.thread && userThread.thread.schedules) ||
                                 [];

      // Skip if the thread doesn't have schedules
      if (userThreadSchedules.length === 0) {
        continue;
      }

      // Check each schedule of the thread against each schedule of the user's registered threads
      for (const newSchedule of thread.schedules) {
        for (const existingSchedule of userThreadSchedules) {
          // Check if the days match
          if (newSchedule.day_of_week === existingSchedule.day_of_week) {
            // Parse time strings to compare times
            const newStart = this.parseTimeString(newSchedule.start_time);
            const newEnd = this.parseTimeString(newSchedule.end_time);
            const existingStart = this.parseTimeString(existingSchedule.start_time);
            const existingEnd = this.parseTimeString(existingSchedule.end_time);

            // Check for time overlap
            if (this.checkTimeOverlap(newStart, newEnd, existingStart, existingEnd)) {
              result.hasConflict = true;
              const courseTitle = userThread.course ? userThread.course.title :
                                 (userThread.thread && userThread.thread.title ? userThread.thread.title : 'Unknown');

              result.conflictMessage = `Занятие в ${this.formatDayOfWeek(newSchedule.day_of_week)} с ${this.formatTime(newSchedule.start_time)} до ${this.formatTime(newSchedule.end_time)} пересекается с курсом "${courseTitle}" (${this.formatTime(existingSchedule.start_time)} - ${this.formatTime(existingSchedule.end_time)})`;
              return result;
            }
          }
        }
      }
    }

    return result;
  }

  /**
   * Parse time string (HH:MM:SS) to minutes since midnight for easier comparison
   * @param timeString Time string in format HH:MM:SS
   * @returns Minutes since midnight
   */
  parseTimeString(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Check if two time ranges overlap
   * @param start1 Start time of first range (in minutes)
   * @param end1 End time of first range (in minutes)
   * @param start2 Start time of second range (in minutes)
   * @param end2 End time of second range (in minutes)
   * @returns True if the time ranges overlap
   */
  checkTimeOverlap(start1: number, end1: number, start2: number, end2: number): boolean {
    // Two ranges overlap if one range starts before the other ends
    return start1 < end2 && start2 < end1;
  }

  /**
   * Clean up resources when component is destroyed
   */
  ngOnDestroy() {
    // Mark component as inactive
    this.isComponentActive = false;

    // Stop periodic updates and clean up subscription
    this.stopPeriodicUpdates();

    // Clean up router events subscription
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
      this.routerSubscription = null;
    }
  }
}
