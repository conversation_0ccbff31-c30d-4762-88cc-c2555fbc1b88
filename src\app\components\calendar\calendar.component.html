<app-breadcrumbs [items]="breadcrumbs"/>

<div class="max-w-screen-xl">
  <div class="grid lg:grid-cols-3 gap-6">
    <!-- Calendar Widget -->
    <div class="lg:col-span-1">
      <div class="card shadow-sm rounded-lg overflow-hidden">
        <div class="p-4 border-b border-border-light">
          <div class="text-sm text-blue-700 font-semibold">Календарь</div>
        </div>
        <div class="p-3">
          <mat-calendar class="border-0 rounded-lg" [(selected)]="selected" (selectedChange)="onDateSelected($event)"></mat-calendar>
        </div>
      </div>

      <!-- Filter Options -->
      <div class="card shadow-sm rounded-lg mt-4 p-4">
        <div class="text-sm text-blue-700 font-semibold mb-3">Фильтры</div>
        <div class="space-y-2">
          <div *ngFor="let option of filterOptions"
               class="flex items-center cursor-pointer p-2 rounded-md hover:bg-tertiary"
               [ngClass]="{'bg-blue-50 text-blue-700': selectedFilter === option.value}"
               (click)="onFilterChange(option.value)">
            <div class="w-3 h-3 rounded-full mr-2"
                 [ngClass]="{
                   'bg-blue-500': option.value === 'semester',
                   'bg-red-500': option.value === 'exam',
                   'bg-green-500': option.value === 'registration',
                   'bg-purple-500': option.value === 'holiday',
                   'bg-gray-500': option.value === 'all'
                 }"></div>
            <span class="text-xs">{{ option.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Events List -->
    <div class="lg:col-span-2">
      <div class="card shadow-sm rounded-lg">
        <div class="p-4 border-b border-border-light">
          <div class="text-sm text-blue-700 font-semibold">Академический календарь</div>
          <p class="text-xs text-gray-600 mt-1">Важные даты и события учебного года</p>
        </div>

        <div class="p-4">
          <!-- Loading state -->
          <div *ngIf="isLoading" class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-sm text-gray-500">Загрузка данных календаря...</p>
          </div>

          <!-- Error state -->
          <div *ngIf="loadError && !isLoading" class="text-center py-8 bg-red-50 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p class="mt-2 text-sm text-red-500">{{ loadError }}</p>
            <button (click)="loadSemesterData()" class="mt-3 px-4 py-2 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600">
              Попробовать снова
            </button>
          </div>

          <!-- Events Timeline -->
          <div *ngIf="!isLoading && !loadError" class="relative">
            <!-- Timeline line -->
            <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

            <!-- Event items -->
            <div *ngFor="let event of filteredEvents; let i = index" class="relative pl-10 pb-6">
              <!-- Timeline dot -->
              <div class="absolute left-2 top-1.5 w-5 h-5 rounded-full border-2 border-white shadow-sm"
                   [ngClass]="{
                     'bg-blue-500': event.type === 'semester',
                     'bg-red-500': event.type === 'exam',
                     'bg-green-500': event.type === 'registration',
                     'bg-purple-500': event.type === 'holiday'
                   }"></div>

              <!-- Event content -->
              <div class="card shadow-sm rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="flex justify-between items-start">
                  <div>
                    <h3 class="text-sm font-medium">{{ event.title }}</h3>
                    <p class="text-xs text-gray-600 mt-1">{{ event.description }}</p>
                  </div>
                  <span class="text-xs px-2 py-1 rounded-full" [ngClass]="getEventTypeClass(event.type)">
                    {{ formatDate(event.date) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Empty state -->
            <div *ngIf="filteredEvents.length === 0 && !isLoading && !loadError" class="text-center py-8">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">Нет событий для отображения</p>
              <p class="text-xs text-gray-400">Попробуйте изменить фильтры или выбрать другую дату</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
