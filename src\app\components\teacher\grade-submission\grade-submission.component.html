<div class="bg-white dark:bg-gray-800 rounded-md shadow-sm p-3 border border-gray-200 dark:border-gray-700" (click)="$event.stopPropagation()">
  <form [formGroup]="gradeForm" (ngSubmit)="submitGrade()" (click)="$event.stopPropagation()" class="space-y-3">
    <!-- Score input -->
    <div>
      <label for="score" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
        {{ 'ASSIGNMENTS.score' | translate }} ({{ 'ASSIGNMENTS.max_score' | translate }}: {{ maxPoints }})
      </label>
      <div class="relative">
        <input
          type="number"
          id="score"
          formControlName="score"
          class="form-input text-xs py-1.5 w-full"
          [min]="0"
          [max]="maxPoints"
          placeholder="{{ 'ASSIGNMENTS.score' | translate }}"
          (click)="$event.stopPropagation()"
        >
        <div *ngIf="gradeForm.get('score')?.invalid && gradeForm.get('score')?.touched"
             class="text-xs text-red-600 mt-0.5">
          {{ getErrorMessage('score') }}
        </div>
      </div>
    </div>

    <!-- Feedback input -->
    <div>
      <label for="feedback" class="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
        {{ 'ASSIGNMENTS.feedback' | translate }}
      </label>
      <div class="relative">
        <textarea
          id="feedback"
          formControlName="feedback"
          rows="3"
          class="form-input text-xs py-1.5 w-full"
          placeholder="{{ 'ASSIGNMENTS.feedback' | translate }}"
          (click)="$event.stopPropagation()"
        ></textarea>
        <div *ngIf="gradeForm.get('feedback')?.invalid && gradeForm.get('feedback')?.touched"
             class="text-xs text-red-600 mt-0.5">
          {{ getErrorMessage('feedback') }}
        </div>
      </div>
    </div>

    <!-- Submit button -->
    <div class="flex justify-end">
      <button
        type="submit"
        [disabled]="isSubmitting || gradeForm.invalid"
        class="btn bg-blue-600 hover:bg-blue-700 text-white text-xs py-1 px-3 rounded-md flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
        (click)="$event.stopPropagation()">
        <span *ngIf="isSubmitting" class="mr-1">
          <svg class="animate-spin h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </span>
        {{ 'ASSIGNMENTS.save_grade' | translate }}
      </button>
    </div>
  </form>
</div>
