<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>EduniteV2</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/png" href="assets/edunitelogo.png">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <style>
    /* Ensure consistent text rendering across browsers */
    html {
      text-rendering: optimizeLegibility;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Fix for Cyrillic text in Firefox */
    @-moz-document url-prefix() {
      html[lang="ru"], html[lang="kk"] {
        font-family: Arial, Helvetica, sans-serif;
      }
    }
  </style>
  <script>
    (function() {
      try {
        const theme = localStorage.getItem('theme');
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        }
      } catch (e) {
        // If localStorage is not available, do nothing
      }
    })();
  </script>
</head>
<body class="mat-typography transition-all duration-300">
  <app-root></app-root>
</body>
</html>
