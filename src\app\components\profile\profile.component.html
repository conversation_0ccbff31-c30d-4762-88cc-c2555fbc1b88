<app-breadcrumbs [items]="breadcrumbs"/>
<div class="max-w-screen-xl mb-5 ">
  <div class="max-w-4xl ">
    <div class="flex justify-between gap-2 mb-4 border p-4 rounded-xl">
      <div class="flex items-center gap-2">
        <div class="rounded-full bg-gray-400 size-20">
          <img src="/assets/icons/student.svg" class="w-full rounded-full" alt="">
        </div>
        <div>
          <div *ngIf="user.name " class="text-lg font-semibold text-red-500 capitalize">{{ user.name }} {{user.surname}}</div>
          <div *ngIf="user.role " class="text-sm text-gray-500 font-medium flex gap-1">
            {{ user.role }}
            <img src="/assets/icons/student.svg" width="16" class="mb-0.5" alt="">
          </div>
          <div class="text-xs text-gray-400 font-normal">Alma<PERSON>, Narxoz</div>
        </div>
      </div>
    </div>
    <div class="mb-4 border p-4 py-6 rounded-xl">
      <div class="flex  justify-between">
        <div class="w-9/12">
          <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1 ">
            <img src="/assets/icons/info.svg" width="20" class="mb-0.5" alt="">
            Personal information
          </div>
          <div class="mt-2 gap-y-5 grid md:grid-cols-2 grid-cols-1 text-xs  text-gray-600 font-medium">
            <div class="">
              <div class="mb-1 ">
                First Name:
              </div>
              <div class="text-gray-700">
                Dwayne
              </div>
            </div>
            <div class="">
              <div class="mb-1 ">
                Last Name:
              </div>
              <div class="text-gray-700">
                Johnson
              </div>
            </div>
            <div class="">
              <div class="mb-1 ">
                Phone
              </div>
              <div class="text-gray-700">
                +7 (*************
              </div>
            </div>
            <div class="">
              <div class="mb-1 ">
                Email Address:
              </div>
              <div class="text-gray-700">
                baikadamoff01&#64;gmail.com
              </div>
            </div>
            <div class="">
              <div class="mb-1 ">
                Bio:
              </div>
              <div class="text-gray-700">
                Digital Engineer
              </div>
            </div>
          </div>
        </div>
        <div>
<!--          <button class="edit border px-4 p-1.5 text-xs rounded text-gray-600 flex items-center gap-1">-->
<!--            <img src="/assets/icons/write.svg" class="invert" alt="" width="12">-->
<!--            <span class="mt-0.5">Edit</span>-->
<!--          </button>-->
        </div>
      </div>
    </div>

    <div *ngIf="user.role != 'teacher'" class="mb-4 border p-4 py-6 rounded-xl">
      <div class="flex  justify-between">
        <div class="w-9/12">
          <div class="text-base font-medium text-gray-800 mb-5 ">Majoring</div>
          <div class="mt-2 gap-y-5 grid md:grid-cols-2 grid-cols-1 text-xs  text-gray-600 font-medium">
            <div class="">
              <div class="mb-1 ">
                Grade:
              </div>
              <div class="text-gray-700">
                1th course
              </div>
            </div>
            <div class="">
              <div class="mb-1 ">
                Majoring:
              </div>
              <div class="text-gray-700">
                Digital Engineering and design
              </div>
            </div>
            <div class="">
              <div class="mb-1 ">
                GPA:
              </div>
              <div class="text-gray-700">
                3.6
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
