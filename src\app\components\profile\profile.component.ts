import {Component, OnInit} from '@angular/core';
import {AuthService} from "../../core/services/auth.service";
import {StorageService} from "../../core/services/storage.service";

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.css'
})
export class ProfileComponent implements OnInit{

  user:any;

  breadcrumbs: { label: string, url?: string }[] = [
    { label: 'Главная', url: '/' },
    { label: 'Профиль', },
  ];

  constructor(private storageService: StorageService) {
  }

  ngOnInit() {
    this.user = this.storageService.getUser()
    console.log(this.user)
  }
}
