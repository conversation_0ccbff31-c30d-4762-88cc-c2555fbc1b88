<app-breadcrumbs [items]="breadcrumbs"/>

<div class="font-normal  mb-2 text-xl ">
  {{ 'MENU.Registration' | translate }}
</div>
<div class=" max-w-screen-2xl ">
  <div class="flex flex-wrap  justify-between">
    <div class="xl:w-[70%] w-full mb-5">
      <form class="example-form">
        <div class="flex flex-wrap gap-3 items-center mb-4">
          <!-- Выбор курса -->
          <div class="text-sm relative w-64">
            <input
              class="w-full rounded bg-[#efedf0] dark:bg-gray-700 border-b border-blue-500 placeholder-gray-600
           dark:placeholder-gray-400 p-3 text-black dark:text-white focus:outline-0 transition-all duration-500 h-[46px]"
              placeholder="Выберите Курс"
              type="text"
              matInput
              [formControl]="courseControl"
              [matAutocomplete]="autoCourse"
            />
            <!-- Small loading indicator inside the input field -->
            <div *ngIf="isLoadingCourses" class="absolute right-3 top-1/2 transform -translate-y-1/2">
              <div class="spinner rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
            <mat-autocomplete #autoCourse="matAutocomplete" [displayWith]="displayCourseTitle" autoActiveFirstOption>
              <mat-option *ngIf="isLoadingCourses" disabled>
                <span class="text-sm text-gray-500">Загрузка курсов...</span>
              </mat-option>
              <mat-option *ngFor="let course of courses" [value]="course">
                <span class="text-sm">{{ course.title }}</span>
              </mat-option>
            </mat-autocomplete>
          </div>

          <!-- Search by title or teacher -->
          <div class="text-sm relative w-64">
            <input
              class="w-full rounded bg-[#efedf0] dark:bg-gray-700 border-b border-blue-500 placeholder-gray-600
           dark:placeholder-gray-400 p-3 text-black dark:text-white focus:outline-0 transition-all duration-500 h-[46px]"
              placeholder="Поиск по названию или преподавателю"
              type="text"
              [(ngModel)]="searchQuery"
              (ngModelChange)="applyFilters()"
              [ngModelOptions]="{standalone: true}"
            />
            <div *ngIf="searchQuery" class="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                 (click)="searchQuery = ''; applyFilters()">
              <span class="text-gray-500 dark:text-gray-300 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200">✕</span>
            </div>
          </div>

          <!-- Day of week filter -->
          <select
            class="rounded p-3 bg-[#efedf0] dark:bg-gray-700 border-b border-blue-500 text-black dark:text-white text-xs h-[46px] w-40"
            [(ngModel)]="dayFilter"
            (ngModelChange)="applyFilters()"
            [ngModelOptions]="{standalone: true}">
            <option value="">Все дни</option>
            <option value="1">Понедельник</option>
            <option value="2">Вторник</option>
            <option value="3">Среда</option>
            <option value="4">Четверг</option>
            <option value="5">Пятница</option>
            <option value="6">Суббота</option>
            <option value="7">Воскресенье</option>
          </select>

          <!-- Available seats filter -->
          <select
            class="rounded p-3 bg-[#efedf0] dark:bg-gray-700 border-b border-blue-500 text-black dark:text-white text-xs h-[46px] w-40"
            [(ngModel)]="availabilityFilter"
            (ngModelChange)="applyFilters()"
            [ngModelOptions]="{standalone: true}">
            <option value="">Все потоки</option>
            <option value="available">Только с местами</option>
            <option value="full">Только заполненные</option>
          </select>

          <!-- Reset button -->
          <button class="rounded p-3 bg-gray-200 hover:bg-gray-50 dark:bg-gray-700 dark:hover:bg-gray-600
            text-gray-800 dark:text-white text-xs border-b border-b-blue-500 h-[46px] flex items-center gap-2"
            (click)="resetFilters()">
            <span class="text-gray-500 dark:text-gray-300">✕</span>
          </button>
        </div>

      </form>
      <div class=" w-full ">
        <div class="overflow-x-auto w-full">
          <!-- Loading indicator for threads -->
          <div *ngIf="isLoadingThreads" class="flex justify-center py-8">
            <div class="spinner rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
          </div>

          <table *ngIf="!isLoadingThreads" class="w-full text-sm text-left rtl:text-right text-gray-700 dark:text-gray-300">
            <thead class="text-gray-700 dark:bg-gray-800 dark:text-gray-300">
            <tr>
              <th scope="col" class="px-6 py-2 text-xs">
                Курс
              </th>
              <th scope="col" class="px-6 py-2 text-xs">
                CRN Потока
              </th>
              <th scope="col" class="px-6 py-2 text-xs">
                Преподаватель
              </th>
              <th scope="col" class="px-6 py-2 text-xs">
                Расписание
              </th>
              <th scope="col" class="px-6 py-2 text-center text-xs text-nowrap">
                Доступно мест
              </th>
              <th scope="col" class="px-6 py-2 text-center text-xs text-nowrap">
                Занято мест
              </th>
              <th scope="col" class="px-6 py-2 text-xs">
                Запись
              </th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let thread of filteredThreads"
                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 border-gray-200">
              <td class="px-6 py-2 text-xs font-medium">
                {{  thread.course.title }}
              </td>
              <td class="px-6 py-2 text-xs">
                {{ thread.title }}
              </td>
              <td class="px-6 py-2 text-xs">
                {{ thread.teacher.name }} {{ thread.teacher.surname }}
              </td>
              <td class="px-6 py-2 text-xs">
                <div *ngIf="thread.schedules && thread.schedules.length > 0">
                  <div *ngFor="let schedule of thread.schedules" class="mb-1">
                    {{ formatDayOfWeek(schedule.day_of_week) }} {{ formatTime(schedule.start_time) }} - {{ formatTime(schedule.end_time) }}
                  </div>
                </div>
                <div *ngIf="!thread.schedules || thread.schedules.length === 0">
                  Расписание не указано
                </div>
              </td>
              <td class="px-6 py-2 text-xs text-center">
                <!-- Color-coded available slots -->
                <span class="font-medium px-2 py-1 rounded-full"
                      [ngClass]="{
                        'bg-green-100 text-green-800': getAvailabilityPercentage(thread) > 50,
                        'bg-yellow-100 text-yellow-800': getAvailabilityPercentage(thread) <= 50 && getAvailabilityPercentage(thread) >= 10,
                        'bg-red-100 text-red-800': getAvailabilityPercentage(thread) < 10
                      }">
                  {{ thread.available_slots }}
                </span>
              </td>
              <td class="px-6 py-2 text-xs text-center">
                <span class="font-medium text-blue-600">{{ thread.booked_slots }}</span>
                <span class="text-gray-500">/{{ thread.max_students }}</span>
                <!-- Show percentage filled -->
                <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                  <div class="h-1.5 rounded-full"
                       [ngStyle]="{'width': (thread.booked_slots / thread.max_students * 100) + '%'}"
                       [ngClass]="{
                         'bg-green-500': getAvailabilityPercentage(thread) > 50,
                         'bg-yellow-500': getAvailabilityPercentage(thread) <= 50 && getAvailabilityPercentage(thread) >= 10,
                         'bg-red-500': getAvailabilityPercentage(thread) < 10
                       }">
                  </div>
                </div>
              </td>
              <td class="px-6 py-2 text-xs">
                <!-- Registration button with conflict check and available seats check -->
                <ng-container *ngIf="!isUserRegistered(thread.id)">
                  <!-- Get conflict info -->
                  <ng-container *ngVar="hasScheduleConflict(thread.id) as conflictInfo">
                    <!-- Button is disabled when there's a conflict or no available seats -->
                    <button
                      (click)="registerToThread(thread.id)"
                      [disabled]="conflictInfo.hasConflict || thread.available_slots <= 0"
                      [class]="(conflictInfo.hasConflict || thread.available_slots <= 0) ?
                        'bg-gray-400 text-white rounded px-3 py-1 flex gap-1 items-center cursor-not-allowed' :
                        'bg-blue-500 text-white rounded px-3 py-1 flex gap-1 items-center hover:bg-blue-600'"
                      [matTooltip]="conflictInfo.hasConflict ? 'Конфликт расписания: ' + conflictInfo.conflictMessage :
                                    (thread.available_slots <= 0 ? 'Нет доступных мест' : '')"
                      matTooltipPosition="above">
                      <img src="/assets/icons/add.svg" class="invert size-3" alt="">
                      <span>
                        Записаться
                      </span>
                    </button>
                  </ng-container>
                </ng-container>

                <button
                  *ngIf="isUserRegistered(thread.id)"
                  (click)="unregisterFromThread(thread.id)"
                  class="bg-red-500 text-white rounded px-3 py-1 flex gap-1 items-center">
                  <img src="/assets/icons/minus.svg" class="invert size-4" alt="">
                  <span>
                    Отменить
                  </span>
                </button>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="xl:w-[25%] w-full">
      <!-- Info panel -->
      <div class="rounded-lg shadow-md bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 w-full text-sm p-5 transition-all duration-300 hover:shadow-lg">
        <div class="flex items-start">
          <div class="bg-blue-50 dark:bg-blue-900 rounded-full p-2 mr-3 text-blue-500 dark:text-blue-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <div class="font-medium text-base mb-1 dark:text-white">Информация</div>
            <p class="text-gray-600 dark:text-gray-300 leading-relaxed">
              Выберите курс, преподавателя и удобное расписание.
              <br>
              После регистрации вы получите подтверждение и подробную информацию.
            </p>
          </div>
        </div>
      </div>

      <!-- Registered courses panel -->
      <div class="rounded-lg shadow-md bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 w-full text-sm p-5 mt-4 transition-all duration-300 hover:shadow-lg">
        <div class="flex items-center mb-4">
          <div class="bg-blue-50 dark:bg-blue-900 rounded-full p-2 mr-3 text-blue-500 dark:text-blue-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <div class="font-medium text-base dark:text-white">Твои зарегистрированные курсы</div>
        </div>

        <!-- Loading indicator for user threads -->
        <div *ngIf="isLoadingUserThreads" class="flex justify-center py-6">
          <div class="spinner rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>

        <div *ngIf="!isLoadingUserThreads">
          <div *ngIf="user_threads.length === 0" class="text-center py-6 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto mb-2 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            У вас пока нет зарегистрированных курсов
          </div>

          <div *ngIf="user_threads.length > 0" class="space-y-3">
            <div *ngFor="let thread of user_threads;let index"
                 class="p-3 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors duration-200 mb-2">
              <div class="font-medium text-blue-700 dark:text-blue-400 mb-1">
                <a href="/thread/{{ thread.thread.id }}" class="hover:underline">{{ thread.course.title }}</a>
              </div>
              <div class="text-xs space-y-1">
                <div class="flex items-center">
                  <span class="text-gray-500 dark:text-gray-400 w-24">Поток:</span>
                  <span class="dark:text-gray-300">{{ thread.thread.title }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-gray-500 dark:text-gray-400 w-24">Преподаватель:</span>
                  <span class="dark:text-gray-300">{{ thread.teacher.name }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-gray-500 dark:text-gray-400 w-24">Семестр:</span>
                  <span class="dark:text-gray-300">{{ thread.semester.name }}</span>
                </div>

                <!-- Schedule information -->
                <div class="mt-2">
                  <div class="text-gray-500 dark:text-gray-400 font-medium mb-1">Расписание:</div>
                  <!-- Get schedules from either thread.schedules or thread.thread.schedules -->
                  <ng-container *ngVar="(thread.schedules || (thread.thread && thread.thread.schedules) || []) as schedules">
                    <div *ngIf="schedules.length > 0" class="ml-2">
                      <div *ngFor="let schedule of schedules" class="flex items-center text-xs mb-1">
                        <span class="inline-block w-6 text-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300 rounded mr-1 py-0.5">
                          {{ formatDayOfWeek(schedule.day_of_week) }}
                        </span>
                        <span class="dark:text-gray-300">{{ formatTime(schedule.start_time) }} - {{ formatTime(schedule.end_time) }}</span>
                      </div>
                    </div>
                    <div *ngIf="schedules.length === 0" class="text-gray-400 dark:text-gray-500 ml-2">
                      Расписание не указано
                    </div>
                  </ng-container>
                </div>

                <div class="mt-3 pt-2 border-t border-gray-100 dark:border-gray-700">
                  <a href="/schedule" class="text-blue-500 dark:text-blue-400 text-xs flex items-center hover:underline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    Перейти к полному расписанию
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



