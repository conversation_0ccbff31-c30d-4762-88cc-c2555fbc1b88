<app-breadcrumbs [items]="breadcrumbs"/>

<div class="max-w-screen-xl">
  <!-- Header and Filters -->
  <div class="card shadow-sm rounded-lg mb-6">
    <div class="p-4 border-b border-border-light">
      <div class="text-sm text-blue-700 font-semibold">Курсы и экзамены</div>
      <p class="text-xs text-gray-600 mt-1">Ваши зарегистрированные курсы и статус экзаменов</p>
    </div>

    <div class="p-4">
      <div class="grid md:grid-cols-3 gap-4">
        <!-- Search -->
        <div class="md:col-span-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
              </svg>
            </div>
            <input
              type="text"
              [(ngModel)]="searchTerm"
              (input)="filterExams()"
              class="block w-full p-2 pl-10 text-xs text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Поиск по курсу, потоку или преподавателю">
          </div>
        </div>

        <!-- Date Picker -->
        <div class="md:col-span-1">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 1v3m5-3v3m5-3v3M1 7h18M5 11h10M2 3h16a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1Z"/>
              </svg>
            </div>
            <input
              type="date"
              [value]="selectedDate ? selectedDate.toISOString().split('T')[0] : ''"
              (change)="onDateSelected($any($event.target).valueAsDate)"
              class="block w-full p-2 pl-10 text-xs text-gray-900 border border-gray-300 rounded-lg bg-gray-50 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Выберите дату">
          </div>
        </div>

        <!-- Reset Button -->
        <div class="md:col-span-1 flex items-center">
          <button
            (click)="resetFilters()"
            class="text-xs px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg flex items-center">
            <svg class="w-3.5 h-3.5 mr-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.7 7.7A7.1 7.1 0 0 0 5 10.8M18 4v4h-4m-7.7 8.3A7.1 7.1 0 0 0 19 13.2M6 20v-4h4"/>
            </svg>
            Сбросить фильтры
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Exam Schedule Table -->
  <div class="card shadow-sm rounded-lg overflow-hidden">
    <!-- Loading state -->
    <div *ngIf="isLoading" class="p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      <p class="mt-2 text-sm text-gray-500">Загрузка расписания экзаменов...</p>
    </div>

    <!-- Error state -->
    <div *ngIf="loadError && !isLoading" class="p-8 text-center bg-red-50">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <p class="mt-2 text-sm text-red-500">{{ loadError }}</p>
      <button (click)="loadExamSchedule()" class="mt-3 px-4 py-2 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600">
        Попробовать снова
      </button>
    </div>

    <!-- Exam data table -->
    <div *ngIf="!isLoading && !loadError" class="overflow-x-auto">
      <table class="w-full text-xs text-left">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
          <tr>
            <th scope="col" class="px-4 py-3">Курс</th>
            <th scope="col" class="px-4 py-3">Поток</th>
            <th scope="col" class="px-4 py-3">Расписание</th>
            <th scope="col" class="px-4 py-3">Преподаватель</th>
            <th scope="col" class="px-4 py-3">Статус экзамена</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let exam of filteredExams" class="bg-white border-b hover:bg-gray-50">
            <td class="px-4 py-3">
              <div class="font-medium">{{ exam.course_title }}</div>
              <div class="text-gray-500">{{ exam.semester_name }}</div>
            </td>
            <td class="px-4 py-3">
              <div class="font-medium">{{ exam.stream_title }}</div>
            </td>
            <td class="px-4 py-3">
              <div class="text-gray-700">{{ formatSchedule(exam.schedules) }}</div>
            </td>
            <td class="px-4 py-3">
              <button class="text-xs px-2 py-1 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded">
                {{ exam.teacher_name }}
              </button>
            </td>
            <td class="px-4 py-3">
              <span class="px-2 py-1 rounded-full text-xs" [ngClass]="getExamStatusClass(exam.exam_status)">
                {{ getExamStatusText(exam.exam_status) }}
              </span>
              <div *ngIf="exam.exam_status === 'not_scheduled'" class="text-xs text-gray-500 mt-1">
                Экзамен будет запланирован позже
              </div>
            </td>
          </tr>

          <!-- Empty state -->
          <tr *ngIf="filteredExams.length === 0 && !isLoading && !loadError">
            <td colspan="5" class="px-4 py-8 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <p class="mt-2 text-sm text-gray-500">Курсы не найдены</p>
              <p class="text-xs text-gray-400">Вы не зарегистрированы ни на один курс</p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Calendar View (Optional) -->
  <div class="card shadow-sm rounded-lg mt-6">
    <div class="p-4 border-b border-border-light">
      <div class="text-sm text-blue-700 font-semibold">Календарь занятий</div>
      <p class="text-xs text-gray-600 mt-1">Визуальное представление расписания занятий</p>
    </div>

    <!-- Loading state -->
    <div *ngIf="isLoading" class="p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      <p class="mt-2 text-sm text-gray-500">Загрузка календаря...</p>
    </div>

    <!-- Error state -->
    <div *ngIf="loadError && !isLoading" class="p-8 text-center">
      <p class="text-sm text-gray-500">Не удалось загрузить календарь</p>
    </div>

    <!-- Calendar content -->
    <div *ngIf="!isLoading && !loadError" class="p-4">
      <div class="text-center mb-4">
        <p class="text-sm text-gray-600">Экзамены еще не запланированы</p>
        <p class="text-xs text-gray-500 mt-1">Информация о датах экзаменов будет доступна позже</p>
      </div>

      <div class="grid md:grid-cols-7 gap-2">
        <!-- Calendar days of week -->
        <ng-container *ngFor="let day of ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']">
          <div class="p-2 border rounded-lg bg-gray-50">
            <div class="text-xs font-medium text-center">{{ day }}</div>
          </div>
        </ng-container>
      </div>

      <div class="mt-4 text-center">
        <button class="px-4 py-2 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600">
          Перейти к расписанию занятий
        </button>
      </div>
    </div>
  </div>
</div>
