<div class="bg-white rounded-lg shadow-sm p-4 mb-6">
  <div class="text-lg font-medium text-blue-700 mb-4">Teacher Management Panel</div>

  <!-- Tabs Navigation -->
  <div class="flex border-b mb-4">
    <button
      class="px-4 py-2 text-sm font-medium transition-all duration-200"
      [ngClass]="{'text-blue-600 border-b-2 border-blue-600': activeTab === 'assignment-groups',
                 'text-gray-500 hover:text-blue-600': activeTab !== 'assignment-groups'}"
      (click)="setActiveTab('assignment-groups')">
      Task Groups
    </button>
    <button
      class="px-4 py-2 text-sm font-medium transition-all duration-200"
      [ngClass]="{'text-blue-600 border-b-2 border-blue-600': activeTab === 'weeks',
                 'text-gray-500 hover:text-blue-600': activeTab !== 'weeks'}"
      (click)="setActiveTab('weeks')">
      Weeks
    </button>
  </div>

  <!-- Assignment Groups Tab -->
  <div *ngIf="activeTab === 'assignment-groups'" class="space-y-4">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-md font-medium">Manage Task Groups</h3>
      <button
        (click)="openAddGroupDialog()"
        class="px-3 py-1.5 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Add Group
      </button>
    </div>

    <div *ngIf="assignmentGroups && assignmentGroups.length === 0" class="text-center py-8 text-gray-500">
      No task groups. Create your first group by clicking the "Add Group" button.
    </div>

    <div *ngIf="assignmentGroups && assignmentGroups.length > 0" class="space-y-3">
      <div *ngFor="let group of assignmentGroups" class="bg-gray-50 p-3 rounded border flex justify-between items-center">
        <div>
          <div class="flex items-center gap-2">
            <span class="font-medium">{{ group.name }}</span>
            <span class="text-xs text-gray-500">({{ group.group_type }})</span>
          </div>
          <div class="text-xs text-gray-600">Weight: {{ group.weight }}</div>
        </div>
        <div class="flex gap-2">
          <button
            (click)="openUpdateGroupDialog(group)"
            class="p-1.5 text-blue-600 hover:bg-blue-50 rounded-full transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
            </svg>
          </button>
          <button
            (click)="openDeleteGroupDialog(group)"
            class="p-1.5 text-red-600 hover:bg-red-50 rounded-full transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Weeks Tab -->
  <div *ngIf="activeTab === 'weeks'" class="space-y-4">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-md font-medium">Manage Weeks</h3>
      <button
        (click)="openAddWeekDialog()"
        class="px-3 py-1.5 bg-blue-600 text-white text-xs rounded hover:bg-blue-700 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        Add Week
      </button>
    </div>

    <div *ngIf="weeks.length === 0" class="text-center py-8 text-gray-500">
      No weeks. Create your first week by clicking the "Add Week" button.
    </div>

    <div *ngIf="weeks.length > 0" class="space-y-3">
      <div *ngFor="let week of weeks" class="bg-gray-50 p-3 rounded border">
        <div class="flex justify-between items-center">
          <div>
            <div class="font-medium">Week #{{ week.week_number }}: {{ week.title }}</div>
            <div *ngIf="week.description" class="text-xs text-gray-600">{{ week.description }}</div>
            <div class="text-xs text-gray-500 mt-1">
              Assignments: {{ week.assignments?.length || 0 }}
            </div>
          </div>
          <div class="flex gap-2">
            <button
              (click)="openUpdateWeekDialog(week)"
              class="p-1.5 text-blue-600 hover:bg-blue-50 rounded-full transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            </button>
            <button
              (click)="openDeleteWeekDialog(week)"
              class="p-1.5 text-red-600 hover:bg-red-50 rounded-full transition-all">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Assignments in this week -->
        <div *ngIf="week.assignments?.length > 0" class="mt-2 pl-4 border-l-2 border-gray-200">
          <div class="text-xs font-medium text-gray-500 mb-1">Assignments in this week:</div>
          <div *ngFor="let item of week.assignments" class="text-xs py-1 flex justify-between items-center">
            <div class="flex items-center gap-1">
              <span *ngIf="item.assignment.type === 'task'" class="w-2 h-2 bg-blue-500 rounded-full"></span>
              <span *ngIf="item.assignment.type === 'info'" class="w-2 h-2 bg-green-500 rounded-full"></span>
              <span>{{ item.assignment.title }}</span>
            </div>
            <div class="text-gray-500">
              {{ item.assignment.type === 'task' ? 'Task' : 'Lecture' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
