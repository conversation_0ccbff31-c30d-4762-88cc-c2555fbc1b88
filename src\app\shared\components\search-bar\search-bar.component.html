<div class="w-full text-xs border border-red-800 rounded-full px-2 py-0.5 flex items-center gap-x-3">

  <!-- Иконка поиска (всегда видима) -->
  <div class="bg-red-800 p-2.5 rounded-full">
    <img src="/assets/icons/search.svg" class="lg:size-4 size-3 invert" alt="Search">
  </div>

  <!-- Поле ввода (всегда видно) -->
  <input type="text" placeholder="{{ 'HOME.HI' | translate }}"
         class="flex-grow bg-transparent dark:placeholder-gray-200 p-2 text-gray-700 focus:outline-0">

  <!-- Фильтры: Выпадающий список на мобильных, кнопки на больших экранах -->
  <div class="xl:hidden   flex items-center">
    <select class="border border-red-800 rounded-xl p-1  text-10 lg:text-xs bg-white dark:bg-gray-800 text-gray-700 dark:text-white">
        <option *ngFor="let filter of filters">{{ filter }}</option>
    </select>
  </div>

  <div class="hidden xl:flex  items-center justify-end gap-x-2">
    <div class="bg-[#cd1c18] text-white text-xs p-1 rounded-full flex gap-x-3">
      <div class="border border-dashed rounded-full p-2">{{ 'SEARCH-BAR.courses' | translate}}</div>
      <div class="flex bg-red-800 items-center border border-dashed rounded-full p-2">{{ 'SEARCH-BAR.teachers' | translate }}</div>
      <div class="border border-dashed rounded-full p-2">{{ 'SEARCH-BAR.tests' | translate }}</div>
    </div>
  </div>

</div>
