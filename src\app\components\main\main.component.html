<div class="max-w-screen-2xl mx-auto px-2 sm:px-4 pb-6">
  <!-- Hero section -->
  <div class="card mb-6 p-6 rounded-xl bg-gradient-to-r from-primary-50 to-primary-100 dark:bg-blue-400 dark:text-blue-100 border-none shadow-md">
    <div class="flex flex-col md:flex-row items-start md:items-center justify-between">
      <div class="mb-4 md:mb-0">
        @if (user.role == 'student') {
          <div class="text-2xl font-semibold mb-2 dark:text-white">{{ 'HOME.HI_STUDENT' | translate }}</div>
          <p class="text-sm text-blue-300 dark:text-blue-200 max-w-xl">{{ 'HOME.remember' | translate }}</p>
        } @else {
          <div class="text-2xl font-semibold mb-2 dark:text-white">{{ 'HOME.HI_TEACHER' | translate }}</div>
          <p class="text-sm text-blue-300 dark:text-blue-200 max-w-xl">{{ 'HOME.remember_teacher' | translate }}</p>
        }
      </div>

      <!-- Quick actions -->
      <div class="flex flex-wrap gap-2">
        <a href="/schedule" class="btn bg-blue-600 hover:bg-blue-700 text-white text-xs py-2 px-4 flex items-center gap-1 shadow-sm dark:bg-blue-500 dark:hover:bg-blue-600">
          <!-- иконка -->
          {{ 'HOME.schedule' | translate }}
        </a>
        <a *ngIf="user.role === 'student'" href="/tasks" class="btn bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-4 flex items-center gap-1 shadow-sm dark:bg-blue-400 dark:hover:bg-blue-500">
          <!-- иконка -->
          {{ 'HOME.ASSIGNMENTS' | translate }}
        </a>
        <a href="/courses" class="btn bg-blue-500 hover:bg-blue-600 text-white text-xs py-2 px-4 flex items-center gap-1 shadow-sm dark:bg-blue-400 dark:hover:bg-blue-500">
          <!-- иконка -->
          {{ 'HOME.COURSES' | translate }}
        </a>
      </div>
    </div>
  </div>


  <!-- Stats cards - moved to the right -->
  <div class="flex justify-end gap-4 mb-6">
    <!-- Notifications stat -->
    <div class="card py-2 px-4 rounded-lg shadow-sm border-l-4 border-l-purple-500 cursor-pointer hover:shadow-md transition-all w-64">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-xs text-tertiary">{{ 'HOME.NOTIFICATIONS' | translate }}</div>
          <div class="text-lg font-semibold">{{ notificationsCount }}</div>
        </div>
        <div class="bg-purple-100 dark:bg-purple-900 p-2 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500 dark:text-purple-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 17H2a3 3 0 0 0 3-3V9a7 7 0 0 1 14 0v5a3 3 0 0 0 3 3zm-8.27 4a2 2 0 0 1-3.46 0"></path>
          </svg>
        </div>
      </div>
      <div class="text-xs text-gray-500 mt-1">{{ 'HOME.no_notifications' | translate }}</div>
    </div>

    <!-- Courses stat -->
    <a [routerLink]="['/courses']" class="card py-2 px-4 rounded-lg shadow-sm border-l-4 border-l-blue-500 cursor-pointer hover:shadow-md transition-all w-64">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-xs text-tertiary">{{ 'HOME.ACTIVE_COURSES' | translate }}</div>
          <div class="text-lg font-semibold">{{ activeCourses }}</div>
        </div>
        <div class="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 dark:text-blue-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
          </svg>
        </div>
      </div>
    </a>

    <!-- Assignments stat - only for students -->
    <a *ngIf="user.role === 'student'" [routerLink]="['/tasks']" class="card py-2 px-4 rounded-lg shadow-sm border-l-4 border-l-amber-500 cursor-pointer hover:shadow-md transition-all w-64">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-xs text-tertiary">{{ 'HOME.PENDING_ASSIGNMENTS' | translate }}</div>
          <div class="text-lg font-semibold">{{ pendingAssignments }}</div>
        </div>
        <div class="bg-amber-100 dark:bg-amber-900 p-2 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-500 dark:text-amber-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M9 11l3 3L22 4"></path>
            <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
          </svg>
        </div>
      </div>
    </a>
  </div>

  <!-- Main content -->
  <div class="flex flex-col lg:flex-row gap-6">
    <!-- Left column -->
    <div class="w-full lg:w-2/3">
      <!-- My Courses section -->
      <div class="card p-5 rounded-lg shadow-sm mb-6">
        <div class="flex justify-between items-center mb-4">
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
              <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
            </svg>
            <div class="text-lg font-semibold">{{ 'HOME.my_courses' | translate }}</div>
          </div>
          <a href="/courses"
             class="text-primary-600 hover:text-primary-700 text-xs font-medium flex items-center gap-1">
            {{ 'HOME.view_all' | translate }}
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
        <app-courses-list [gridClasses]="'grid xl:grid-cols-2 grid-cols-1 gap-4'"></app-courses-list>
      </div>

      <!-- News section -->
      <div class="card p-5 rounded-lg shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <div class="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-primary-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1M15 7l4 4-4 4"></path>
              <path d="M4 12h15"></path>
            </svg>
            <div class="text-lg font-semibold">{{ 'NEWS.title' | translate }}</div>
          </div>
          <a href="/news"
             class="text-primary-600 hover:text-primary-700 text-xs font-medium flex items-center gap-1">
            {{ 'HOME.view_all' | translate }}
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M5 12h14"></path>
              <path d="M12 5l7 7-7 7"></path>
            </svg>
          </a>
        </div>
        <app-news-list [newsList]="latestNews" [showContent]="false" [limit]="3"></app-news-list>
      </div>
    </div>

    <!-- Right column -->
    <div class="w-full lg:w-1/3">
      <!-- Tasks widget - only shown for students -->
      <div *ngIf="user.role === 'student'" class="card rounded-lg shadow-sm mb-6 overflow-hidden bg-white dark:bg-gray-800 text-primary-900 dark:text-gray-100">
        <div class="p-4 border-b border-border-light bg-gradient-to-r from-primary-50 to-primary-100 dark:from-gray-800 dark:to-gray-700 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600 dark:text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M9 11l3 3L22 4"></path>
                <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
              </svg>
              <div class="text-sm font-semibold text-primary-700 dark:text-gray-200">
                {{ 'HOME.UPCOMING_TASKS' | translate }}
              </div>
            </div>
            <a href="/tasks" class="text-xs text-primary-600 hover:text-primary-700 dark:text-gray-300 dark:hover:text-white">
              {{ 'HOME.view_all' | translate }}
            </a>
          </div>
        </div>
        <div class="p-4 max-h-96 overflow-y-auto bg-white dark:bg-gray-800 dark:text-gray-200">
          <app-tasks [showDescription]="false"></app-tasks>
        </div>
      </div>


      <!-- Calendar widget -->
      <div class="card rounded-lg shadow-sm overflow-hidden bg-white dark:bg-gray-800 text-primary-900 dark:text-gray-100">
        <div class="p-4 border-b border-border-light bg-gradient-to-r from-primary-50 to-primary-100 dark:from-gray-800 dark:to-gray-700 dark:border-gray-700">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-primary-600 dark:text-gray-300" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              <div class="text-sm font-semibold text-primary-700 dark:text-gray-200">
                {{ 'HOME.CALENDAR' | translate }}
              </div>
            </div>
            <a href="/academic/calendar" class="text-xs text-primary-600 hover:text-primary-700 dark:text-gray-300 dark:hover:text-white">
              {{ 'HOME.view_all' | translate }}
            </a>
          </div>
        </div>
        <div class="p-3 dark:bg-gray-800 dark:text-gray-200">
          <mat-calendar class="border-0 rounded-lg calendar-small dark:bg-gray-800" [(selected)]="selected"></mat-calendar>
          <div class="mt-3 flex items-center gap-x-2">
            <a href="/schedule" class="btn btn-primary w-full text-center text-xs py-2 flex items-center justify-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
              {{ 'HOME.schedule' | translate }}
            </a>
            <button (click)="reload()" class="p-2 rounded-full bg-tertiary hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M23 4v6h-6"></path>
                <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
