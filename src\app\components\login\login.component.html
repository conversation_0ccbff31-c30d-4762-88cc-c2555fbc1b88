<div class="h-full bg-secondary transition-all duration-300">
  <div class="absolute top-4 right-4 flex items-center space-x-2">
    <app-language-toggle></app-language-toggle>
    <app-theme-toggle></app-theme-toggle>
  </div>

  <div class="max-w-screen-xl mx-auto">
    <div class="flex justify-center h-screen items-center">
      <div class="card w-[380px] shadow-md rounded-lg p-8 py-9">
        <div class="flex items-center mb-6">
          <img src="/assets/edunitelogo.png" class="h-12 w-12 rounded-full mr-4" alt="Logo">
          <div>
            <div class="text-2xl font-semibold">{{ 'LOGIN.edunite' | translate }}</div>
            <div class="text-sm text-tertiary">{{ 'LOGIN.lms' | translate }}</div>
          </div>
        </div>

        <div class="mb-6">
          <div class="text-lg font-medium ">{{ 'LOGIN.welcome' | translate }}</div>
          <div class="text-sm text-tertiary">{{ 'LOGIN.continue' | translate }}</div>
        </div>

        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <div class="mb-4">
            <label for="username" class="block text-xs mb-1">{{ 'LOGIN.email' | translate }}</label>
            <input
              id="username"
              formControlName="username"
              type="email"
              placeholder="{{ 'LOGIN.email_placeholder' | translate }}"
              class="form-input text-sm py-2"
            >
            <div *ngIf="loginForm.controls.username?.invalid && loginForm.controls.username?.touched"
                 class="text-xs text-red-600 mt-1">
              {{ 'LOGIN.required' | translate }} 
            </div>
          </div>

          <div class="mb-6">
            <label for="password" class="block text-xs mb-1">{{ 'LOGIN.password' | translate }}</label>
            <input
              id="password"
              formControlName="password"
              type="password"
              placeholder="••••••••"
              class="form-input text-sm py-2"
            >
            <div *ngIf="loginForm.controls.password?.invalid && loginForm.controls.password?.touched"
                 class="text-xs text-red-600 mt-1">
              <ng-container *ngIf="loginForm.controls.password?.errors?.['required']">
                {{ 'LOGIN.required' | translate }} 
              </ng-container>
              <ng-container *ngIf="loginForm.controls.password?.errors?.['minlength']">
                {{ 'LOGIN.minlength' | translate }} {{ 'LOGIN.password' | translate }} {{ 'LOGIN.minlength_count' | translate }} 6 {{ 'LOGIN.characters' | translate }}. 
              </ng-container>
            </div>
          </div>

          <div class="flex justify-between items-center">
            <button
              type="submit"
              [disabled]="isSubmitting"
              class="btn btn-primary text-sm py-2"
            >
              {{ isSubmitting ? ('LOGIN.signing' | translate) : ('LOGIN.sign_in' | translate) }}
            </button>


          </div>

          <div *ngIf="errorMessage" class="mt-4 p-2 bg-red-100 text-red-600 rounded text-xs">
            {{ errorMessage }}
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<!--<div class="max-w-sm mx-auto mt-10 p-4 border rounded">-->
<!--  <h2 class="text-xl font-bold mb-4">Login</h2>-->
<!--  <form (ngSubmit)="onSubmit()">-->
<!--    <input type="email" [(ngModel)]="username" name="username" placeholder="Email" required class="border p-2 w-full mb-2"/>-->
<!--    <input type="password" [(ngModel)]="password" name="password" placeholder="Password" required class="border p-2 w-full mb-4"/>-->
<!--    <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Login</button>-->
<!--  </form>-->
<!--  <p *ngIf="error" class="text-red-500 mt-2">{{ error }}</p>-->
<!--</div>-->
