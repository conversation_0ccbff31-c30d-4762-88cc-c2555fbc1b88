{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"edunite_v2": {"projectType": "application", "schematics": {"@schematics/angular:component": {"standalone": false}, "@schematics/angular:directive": {"standalone": false}, "@schematics/angular:pipe": {"standalone": false}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/edunite_v2", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "6kB", "maximumError": "10kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "edunite_v2:build:production"}, "development": {"buildTarget": "edunite_v2:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "src/assets", "output": "assets"}], "styles": ["@angular/material/prebuilt-themes/azure-blue.css", "src/styles.css"], "scripts": []}}}}}, "cli": {"analytics": false}}