/* Submission section styles */
.submission-section {
  transition: all 0.3s ease-in-out;
}

.submission-card {
  transition: all 0.25s ease-in-out;
  position: relative;
  overflow: hidden;
}

.submission-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.submission-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: #3b82f6;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.submission-card:hover::before {
  opacity: 1;
}

/* Collapsible submission styles */
.submission-card {
  cursor: pointer;
}

/* Expanded content animation */
@keyframes expandContent {
  from { opacity: 0; max-height: 0; }
  to { opacity: 1; max-height: 2000px; }
}

.submission-card > div:last-child {
  animation: expandContent 0.4s ease-out forwards;
  overflow: hidden;
}

/* Line clamp for comment preview */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* File attachment styles */
.file-attachment {
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.file-attachment:hover {
  transform: translateY(-1px);
}

/* Search and filter styles */
input[type="text"], select {
  transition: all 0.2s ease-in-out;
}

input[type="text"]:focus, select:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Filter badge styles */
.filter-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #e5e7eb;
  color: #4b5563;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease-in-out;
}

.filter-badge:hover {
  background-color: #d1d5db;
}

.filter-badge .close-icon {
  margin-left: 0.25rem;
  height: 0.875rem;
  width: 0.875rem;
  cursor: pointer;
}

/* Animation for filter changes */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.filtered-item {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Breadcrumbs loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Button hover effects */
button {
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Dark mode styles */
:host-context(.dark) .submission-card {
  background-color: #1f2937;
  border-color: #374151;
}

:host-context(.dark) .submission-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

:host-context(.dark) .file-attachment {
  background-color: #374151;
  color: #d1d5db;
}

:host-context(.dark) .file-attachment:hover {
  background-color: #4b5563;
}

/* Fade-in animation for content */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.max-w-screen-xl {
  animation: fadeIn 0.4s ease-out;
}