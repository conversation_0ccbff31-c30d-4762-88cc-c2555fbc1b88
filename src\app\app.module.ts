import { NgModule } from '@angular/core';
import { BrowserModule, provideClientHydration } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { NoSidebarLayoutComponent } from "./layout/no-sidebar-layout/no-sidebar-layout.component";
import { WithSidebarLayoutComponent } from "./layout/with-sidebar-layout/with-sidebar-layout.component";
import { SideBarComponent } from "./components/side-bar/side-bar.component";
import { HeaderComponent } from "./components/header/header.component";
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { NotFoundComponent } from "./components/not-found/not-found.component";
import { SharedModule } from "./shared/shared.module";
import { ComponentsModule } from "./components/components.module";
import { TranslateHttpLoader } from "@ngx-translate/http-loader";
import { HttpClient, provideHttpClient, withInterceptors } from "@angular/common/http";
import { TranslateLoader, TranslateModule } from "@ngx-translate/core";
import { registerLocaleData } from "@angular/common";
import localeRu from '@angular/common/locales/ru';
import { httpInterceptor } from "./core/interceptors/http.interceptor";
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { NativeDateAdapter, provideNativeDateAdapter } from '@angular/material/core';

// Функция загрузчика переводов
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}
registerLocaleData(localeRu);


@NgModule({
  declarations: [
    AppComponent,
    NoSidebarLayoutComponent,
    WithSidebarLayoutComponent,
    SideBarComponent,
    NotFoundComponent
  ],
  imports: [
    BrowserModule,
    CommonModule,
    AppRoutingModule,
    SharedModule,
    HeaderComponent,
    TranslateModule.forRoot({
      defaultLanguage: 'en',
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
  ],
  providers: [
    provideHttpClient(
      withInterceptors([httpInterceptor])
    ),
    provideClientHydration(),
    provideAnimationsAsync(),
    provideNativeDateAdapter(),
    { provide: MAT_DATE_LOCALE, useValue: 'ru-RU' }
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}
