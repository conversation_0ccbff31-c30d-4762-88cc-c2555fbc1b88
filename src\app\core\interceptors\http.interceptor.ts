import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, switchMap, throwError } from 'rxjs';
import { StorageService } from '../services/storage.service';
import { AuthService } from '../services/auth.service';

export const httpInterceptor: HttpInterceptorFn = (req, next) => {
  const storageService = inject(StorageService);
  const authService = inject(AuthService);

  const token = storageService.getToken();

  let authReq = req;
  if (token) {
    authReq = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  return next(authReq).pipe(
    catchError((error) => {
      if (error.status === 401) {
        return authService.refreshAccessToken().pipe(
          switchMap((res: any) => {
            const newAccessToken = res.access;
            storageService.saveToken(newAccessToken);

            const newAuthReq = req.clone({
              setHeaders: {
                Authorization: `Bearer ${newAccessToken}`,
              },
            });

            return next(newAuthReq);
          }),
          catchError((refreshError) => {
            console.error('Не удалось обновить токен', refreshError);
            authService.logout(); // или перенаправить на /login
            return throwError(() => refreshError);
          })
        );
      } else {
        return throwError(() => error);
      }
    })
  );
};
