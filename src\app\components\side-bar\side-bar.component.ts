import {Component, OnInit, Input, Output, EventEmitter} from '@angular/core';
import {AuthService} from "../../core/services/auth.service";
import {Router} from '@angular/router';

@Component({
  selector: 'app-side-bar',
  templateUrl: './side-bar.component.html',
  styleUrls: ['./side-bar.component.css'],
})
export class SideBarComponent implements OnInit {
  @Input() collapsed = false;
  @Output() toggleSidebarEvent = new EventEmitter<void>();

  user: any;

  constructor(
    private authService: AuthService,
    private router: Router
  ) {
  }

  ngOnInit() {
    this.user = this.authService.getCurrentUser();
    this.filterMenuItemsByRole();
  }

  filterMenuItemsByRole() {
    // Create a deep copy of the menu structure
    this.menuSections = [];

    // Filter main section items based on role
    const mainItems = this.allMenuItems.main.items.filter(item => {
      // Hide tasks for teachers
      if (item.href === '/tasks') {
        return this.user.role === 'student';
      }
      // Hide ratings for teachers
      if (item.href === '/ratings') {
        return this.user.role === 'student';
      }
      return true;
    });

    // Add main section
    this.menuSections.push({
      title: this.allMenuItems.main.title,
      items: mainItems
    });

    // Add academic section with filtered items
    const academicItems = this.allMenuItems.academic.items.filter(item => {
      // If item is marked as studentOnly, only show it for students
      if (item.studentOnly) {
        return this.user.role === 'student';
      }
      return true;
    });

    if (academicItems.length > 0) {
      this.menuSections.push({
        title: this.allMenuItems.academic.title,
        items: academicItems
      });
    }
  }

  toggleSidebar() {
    this.toggleSidebarEvent.emit();
  }

  allMenuItems = {
    main: {
      title: 'MENU.Main',
      items: [
        {title: 'MENU.Dashboard', icon: '/assets/icons/dashboard.svg', href: '/'},
        {title: 'MENU.Courses', icon: '/assets/icons/courses.svg', href: '/courses'},
        {title: 'MENU.Schedule', icon: '/assets/icons/schedule.svg', href: '/schedule'},
        {title: 'MENU.Assignments', icon: '/assets/icons/assignments.svg', href: '/tasks'},
        {title: 'MENU.Ratings', icon: '/assets/icons/ratings.svg', href: '/ratings'},
        {title: 'MENU.News', icon: '/assets/icons/news.svg', href: '/news'},
      ]
    },
    academic: {
      title: 'MENU.Academic Process',
      items: [
        {title: 'MENU.Registration', icon: '/assets/icons/register.svg', href: '/register', studentOnly: true},
        {title: 'MENU.Academic Calendar', icon: '/assets/icons/calendar.svg', href: '/academic/calendar'},
        {title: 'MENU.Exam Schedule', icon: '/assets/icons/exam.svg', href: '/academic/exam-schedule'},
        {title: 'MENU.Documents', icon: '/assets/icons/files.svg', href: '/documents/docs'},
      ]
    }
  };

  menuSections: any[] = [];

  logout() {
    this.authService.logout();
  }
}

