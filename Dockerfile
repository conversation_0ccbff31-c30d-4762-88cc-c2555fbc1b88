# Development mode Dockerfile for Angular
FROM node:18

WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Expose port 4200 for Angular dev server
EXPOSE 4200

# Start the Angular development server with host 0.0.0.0 to make it accessible outside the container
CMD ["npm", "run", "start", "--", "--host", "0.0.0.0", "--port", "4200", "--disable-host-check"]
