<header class="header w-full overflow-hidden">
  <div class="flex items-center">
    <button
      (click)="toggleSidebar()"
      class="mr-2 p-2 rounded-md hover:bg-tertiary lg:hidden focus:outline-none"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
      </svg>
    </button>
    <app-bread-crumbs></app-bread-crumbs>
  </div>

  <div class="flex items-center space-x-2">
    <!-- <div class="relative hidden md:block">
      <svg xmlns="http://www.w3.org/2000/svg" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-tertiary" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="11" cy="11" r="8"></circle>
        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
      </svg>
      <input
        type="text"
        placeholder="{{ 'HEADER.SEARCH' | translate }}"
        class="pl-10 pr-4 py-2 bg-tertiary rounded-lg focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-transparent w-40"
      />
    </div> -->

    <app-language-toggle></app-language-toggle>
    <app-theme-toggle></app-theme-toggle>


    <div class="relative">
      <button
        class="p-2 rounded-md hover:bg-tertiary focus:outline-none"
        (click)="toggleNotifications()"
        title="{{ 'NOTIFICATIONS.NO_NOTIFICATIONS' | translate }}"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
          <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
        </svg>
      </button>
    </div>

    <div class="flex items-center ml-2">
      <button
        class="flex items-center focus:outline-none"
        (click)="toggleUserMenu()"
      >
        <div class="w-8 h-8 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
            <circle cx="12" cy="7" r="4"></circle>
          </svg>
        </div>
        <span *ngIf="user" class="ml-2 text-sm font-semibold text-sky-400 hidden md:block">
          {{ user.name }} /
          <span class="text-gray-500 capitalize">{{user.role}}</span>
        </span>
      </button>
    </div>

    <!-- User dropdown menu moved outside the header -->
  </div>
</header>

<!-- Mobile backdrop -->
<div *ngIf="userMenuOpen" class="md:hidden fixed inset-0 bg-black bg-opacity-20 z-40" (click)="closeUserMenu()"></div>

<!-- User dropdown menu - positioned outside the header -->
<div *ngIf="userMenuOpen" (clickOutside)="closeUserMenu()" class="fixed right-4 top-16 w-48 card rounded-md shadow-lg py-1 z-50 border">
  <div class="px-4 py-2 border-b">
    <div class="text-sm font-semibold">{{ user?.name }}</div>
  </div>

  <a routerLink="/profile" class="flex w-full items-center px-4 py-1.5 text-xs hover:bg-tertiary">
    <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
      <circle cx="12" cy="7" r="4"></circle>
    </svg>
    {{ 'HEADER.PROFILE' | translate }}
  </a>

  <button
    (click)="logout()"
    class="flex w-full items-center px-4 py-1.5 text-xs hover:bg-tertiary"
  >
    <svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
      <polyline points="16 17 21 12 16 7"></polyline>
      <line x1="21" y1="12" x2="9" y2="12"></line>
    </svg>
    {{ 'HEADER.SIGN_OUT' | translate }}
  </button>
</div>
