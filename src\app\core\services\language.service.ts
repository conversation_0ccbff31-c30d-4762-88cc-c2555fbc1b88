import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private languageKey = 'language';
  private languageLoadedSubject = new BehaviorSubject<boolean>(false);
  // Observable для отслеживания готовности языка
  languageLoaded$ = this.languageLoadedSubject.asObservable();

  constructor(private translate: TranslateService) {
    this.initLanguage();
  }

  private initLanguage(): void {
    if (this.isBrowser()) {
      const savedLang = localStorage.getItem(this.languageKey) || 'en';
      this.translate.setDefaultLang('en');
      this.translate.use(savedLang).subscribe(() => {
        // Set the lang attribute on the HTML element
        document.documentElement.lang = savedLang;
        // После загрузки языка устанавливаем флаг в true
        this.languageLoadedSubject.next(true);
      });
    } else {
      // Если не в браузере, считаем, что язык загружен
      this.languageLoadedSubject.next(true);
    }
  }

  changeLanguage(lang: string): void {
    if (this.isBrowser()) {
      // Перед переключением языка ставим флаг в false (начало загрузки)
      this.languageLoadedSubject.next(false);
      this.translate.use(lang).subscribe(() => {
        localStorage.setItem(this.languageKey, lang);
        // Update the lang attribute on the HTML element
        document.documentElement.lang = lang;
        // После завершения переключения языка ставим флаг в true
        this.languageLoadedSubject.next(true);
      });
    }
  }

  getCurrentLanguage(): string {
    return this.translate.currentLang;
  }

  private isBrowser(): boolean {
    return typeof window !== 'undefined' && typeof localStorage !== 'undefined';
  }
}
