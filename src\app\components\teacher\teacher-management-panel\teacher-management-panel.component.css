/* Animations for tab transitions */
.space-y-4 > * {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects for items */
.bg-gray-50:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease-in-out;
}

/* Dark mode support */
:host-context(.dark) .bg-white {
  background-color: #1f2937;
  color: #f3f4f6;
}

:host-context(.dark) .bg-gray-50 {
  background-color: #374151;
  border-color: #4b5563;
}

:host-context(.dark) .text-gray-500,
:host-context(.dark) .text-gray-600 {
  color: #9ca3af;
}

:host-context(.dark) .border-gray-200 {
  border-color: #374151;
}

:host-context(.dark) .text-blue-700 {
  color: #60a5fa;
}

:host-context(.dark) .text-blue-600 {
  color: #3b82f6;
}

:host-context(.dark) .hover\:bg-blue-50:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

:host-context(.dark) .hover\:bg-red-50:hover {
  background-color: rgba(239, 68, 68, 0.1);
}
