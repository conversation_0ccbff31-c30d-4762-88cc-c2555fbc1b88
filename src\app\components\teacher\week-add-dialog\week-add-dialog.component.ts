import {Component, Inject} from '@angular/core';
import {Form<PERSON>uilder, FormGroup, Validators} from "@angular/forms";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";

@Component({
  selector: 'app-week-add-dialog',
  templateUrl: './week-add-dialog.component.html',
  styleUrl: './week-add-dialog.component.css'
})
export class WeekAddDialogComponent {
  weekForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<WeekAddDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public threadId: number
  ) {
    this.weekForm = this.fb.group({
      week_number: [null, [Validators.required, Validators.min(1)]],
      type: ['', Validators.required],
      title: ['', Validators.required],
      description: ['']
    });
  }

  submit() {
    if (this.weekForm.invalid) return;
    this.dialogRef.close(this.weekForm.value); // возвращаем данные в родительский компонент
  }

  cancel() {
    this.dialogRef.close(); // отмена
  }
}
