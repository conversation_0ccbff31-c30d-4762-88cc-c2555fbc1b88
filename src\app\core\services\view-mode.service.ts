import { Injectable } from '@angular/core';
import { BehaviorSubject } from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class ViewModeService {

  private storageKey = 'viewMode'; // Ключ для localStorage
  private viewModeSource = new BehaviorSubject<'card' | 'columns'>(this.getStoredViewMode());
  viewMode$ = this.viewModeSource.asObservable();

  // Add search query behavior subject
  private searchQuerySource = new BehaviorSubject<string>('');
  searchQuery$ = this.searchQuerySource.asObservable();

  constructor() {}

  private getStoredViewMode(): 'card' | 'columns' {
    return (localStorage.getItem(this.storageKey) as 'card' | 'columns') || 'card';
  }

  setViewMode(mode: 'card' | 'columns') {
    localStorage.setItem(this.storageKey, mode);
    this.viewModeSource.next(mode);
  }

  // Method to set search query
  setSearchQuery(query: string) {
    this.searchQuerySource.next(query);
  }

  // Method to get current search query
  getSearchQuery(): string {
    return this.searchQuerySource.getValue();
  }
}
