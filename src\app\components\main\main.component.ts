import {Component, HostListener, model, OnInit} from '@angular/core';
import {provideNativeDateAdapter} from "@angular/material/core";
import {MOCK_DATA} from "../../shared/mock-data";
import {CourseService} from "../../core/services/course.service";
import {AuthService} from "../../core/services/auth.service";
import {ThreadService} from "../../core/services/thread.service";
import {AssignmentService} from "../../core/services/assignment.service";

@Component({
  selector: 'app-main',
  templateUrl: './main.component.html',
  styleUrl: './main.component.css',
  providers: [provideNativeDateAdapter()],
})
export class MainComponent implements OnInit {
  // Calendar
  selected = model<Date | null>(null);

  // Data
  latestNews = MOCK_DATA.latestNews;
  user: any;
  userThreads: any[] = [];

  // Stats
  activeCourses: number = 0;
  pendingAssignments: number = 0;
  classesToday: number = 3; // Mock value for classes today
  newAnnouncements: number = 0;
  notificationsCount: number = 0;

  // UI state
  isSearchOpen = false;
  screenSize: 'sm' | 'md' | 'lg' | 'xl' = 'xl';

  constructor(
    private courseService: CourseService,
    private authService: AuthService,
    private threadService: ThreadService,
    private assignmentService: AssignmentService
  ) {}

  toggleSearch() {
    this.isSearchOpen = !this.isSearchOpen;
  }

  @HostListener('window:resize', [])
  checkScreenSize() {
    const width = window.innerWidth;
    if (width < 640) {
      this.screenSize = 'sm';
    } else if (width < 1024) {
      this.screenSize = 'md';
    } else if (width < 1280) {
      this.screenSize = 'lg';
    } else {
      this.screenSize = 'xl';
    }
  }

  ngOnInit() {
    // Get user info
    this.user = this.authService.getCurrentUser();

    // Get user threads (courses)
    if (this.user) {
      this.getUserThreads(this.user.id);
    }

    // Get pending assignments
    this.calculatePendingAssignments();

    // Get new announcements
    this.newAnnouncements = this.latestNews.filter(news =>
      new Date(news.pub_date).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000
    ).length;
  }

  getUserThreads(userId: number) {
    this.threadService.getListOfThreadsForUser(userId).subscribe({
      next: (data: any) => {
        this.userThreads = data.threads || [];
        this.activeCourses = this.userThreads.length;
      },
      error: (err) => {
        console.error('Error fetching user threads:', err);
        this.activeCourses = 0;
      }
    });
  }

  calculatePendingAssignments() {
    // Only fetch pending assignments for students
    if (this.user && this.user.role === 'student' && this.user.id) {
      this.assignmentService.getPendingAssignments(this.user.id).subscribe({
        next: (data) => {
          if (data && data.assignments) {
            // Filter out assignments with type "info"
            const filteredAssignments = data.assignments.filter((a: any) => a.type !== 'info');
            this.pendingAssignments = filteredAssignments.length;
          } else {
            this.pendingAssignments = 0;
          }
        },
        error: (err) => {
          console.error('Error fetching pending assignments:', err);
          this.pendingAssignments = 0;
        }
      });
    } else {
      this.pendingAssignments = 0;
    }
  }

  reload() {
    window.location.reload();
  }
}
