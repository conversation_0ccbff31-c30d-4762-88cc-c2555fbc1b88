<div class="relative  inline-block text-left dropdown-container">
  <div>
    <button
      type="button"
      (click)="toggleDropdown()"
      class="inline-flex w-full justify-center items-center gap-x-1.5
      rounded-md bg-white px-3 py-2 text-xs font-medium  text-gray-900 ring-1 shadow-xs ring-gray-300 ring-inset
       hover:bg-gray-50 dark:bg-[#0d1117] dark:text-white"

      id="menu-button"
      [attr.aria-expanded]="isOpen"
      aria-haspopup="true">
      {{ buttonLabel }}
      <svg class="-mr-1 size-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
        <path fill-rule="evenodd" d="M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>

  <div *ngIf="isOpen"
       [@menuAnimation]
       class="absolute right-0 z-10 mt-2 min-w-40 origin-top-right rounded-md bg-white
        dark:bg-[#0d1117] ring-1 shadow-lg ring-black/5 "
       role="menu"
       aria-orientation="vertical"
       aria-labelledby="menu-button"
       tabindex="-1">
    <div class="py-1" role="none">
      <ng-container *ngFor="let item of items; let i = index">
        <!-- Если элемент типа ссылки -->
        <a *ngIf="item.type !== 'button'" [href]="item.url || '#'" class="block  dark:text-white
         px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1">
          <!-- Если задан icon, отображаем его -->
          <img *ngIf="item.icon" [src]="item.icon" class="inline-block mr-2 w-4 h-4 dark:invert" alt="" />
          {{ item.label }}
        </a>
        <!-- Если элемент типа кнопки -->
        <button *ngIf="item.type === 'button'" type="button" (click)="onItemClick(item)" class="block
        w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-white" role="menuitem" tabindex="-1">
          <img *ngIf="item.icon" [src]="item.icon" class="inline-block mr-2 w-4 h-4 dark:invert" alt="" />
          {{ item.label }}
        </button>
      </ng-container>
    </div>
  </div>
</div>
