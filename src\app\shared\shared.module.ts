import {NgModule} from '@angular/core';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>licePipe} from '@angular/common';
import {ThemeToggleComponent} from "./components/theme-toggle.component";
import {DropdownComponent} from "./components/dropdown/dropdown.component";
import {CoursesListComponent} from "./components/courses-list/courses-list.component";
import {SearchBarComponent} from "./components/search-bar/search-bar.component";
import {TranslatePipe, TranslateModule} from "@ngx-translate/core";
import {NewsListComponent} from "./components/news-list/news-list.component";
import {RouterLink} from "@angular/router";
import { BreadcrumbsComponent } from './components/breadcrumbs/breadcrumbs.component';


@NgModule({
  declarations: [
    SearchBarComponent,
    CoursesListComponent,
    DropdownComponent,
    NewsListComponent
  ],
  imports: [
    ThemeToggleComponent,
    NgForOf,
    SlicePipe,
    <PERSON>I<PERSON>,
    Translate<PERSON>ipe,
    NgStyle,
    NgClass,
    RouterLink,
    TranslateModule,
    BreadcrumbsComponent
  ],
  exports: [
    ThemeToggleComponent,
    CoursesListComponent,
    SearchBarComponent,
    NewsListComponent,
    DropdownComponent,
    BreadcrumbsComponent
  ]
})
export class SharedModule {
}
