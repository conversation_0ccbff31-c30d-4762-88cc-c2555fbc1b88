<app-breadcrumbs [items]="breadcrumbs"/>

<!-- Main container -->
<div class="max-w-screen-xl mb-8">

  <!-- Page header with tabs -->
  <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
    <div class="mb-4 sm:mb-0">
      <h1 class="text-xl font-semibold mb-1">{{ 'RATINGS.TITLE' | translate }}</h1>
      <p class="text-sm text-tertiary">{{ 'RATINGS.SUBTITLE' | translate }}</p>
    </div>

    <!-- Tab navigation -->
    <div class="tab-nav">
      <div
        class="tab-item"
        [class.active]="activeTab === 'current'"
        (click)="setActiveTab('current')">
        {{ 'RATINGS.CURRENT_TERM' | translate }}
      </div>
      <div
        class="tab-item"
        [class.active]="activeTab === 'history'"
        (click)="setActiveTab('history')">
        {{ 'RATINGS.HISTORY' | translate }}
      </div>
    </div>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center my-12">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
  </div>

  <!-- Current term view -->
  <div *ngIf="!isLoading && activeTab === 'current' && !selectedCourse">
    <div class="w-full flex flex-wrap justify-between gap-6">
      <!-- Left column - Course cards -->
      <div class="xl:w-[70%] lg:w-2/3 w-full mb-5">
        <!-- Student view with registered courses -->
        <div *ngIf="!isTeacher">
          <!-- Loading indicator for registered courses -->
          <div *ngIf="isLoadingRegisteredCourses" class="flex justify-center my-6">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
          </div>

          <!-- Message about final grades -->
          <div *ngIf="!isLoadingRegisteredCourses" class="card p-5 mb-6 rounded-lg shadow-sm border-l-4 border-blue-500">
            <div class="flex items-start">
              <div class="flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Информация об оценках</h3>
                <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  <p>Итоговые оценки по курсам будут выставлены после окончания семестра. На данный момент вы зарегистрированы на следующие курсы:</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Registered courses list -->
          <div *ngIf="!isLoadingRegisteredCourses && registeredCourses.length > 0" class="card p-4 rounded-lg shadow-sm mb-6">
            <h3 class="text-base font-medium mb-3">Ваши зарегистрированные курсы</h3>
            <div class="space-y-3">
              <div *ngFor="let item of registeredCourses" class="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="font-medium text-sm">{{ item.course?.title || 'Курс' }}</div>
                    <div class="text-xs text-tertiary mt-1">{{ item.course?.code || 'Код курса' }}</div>
                  </div>
                  <div class="text-xs py-1 px-2 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full">
                    Ожидание оценки
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- No registered courses message -->
          <div *ngIf="!isLoadingRegisteredCourses && registeredCourses.length === 0" class="card p-4 rounded-lg shadow-sm mb-6 text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p class="text-sm text-gray-500">Вы не зарегистрированы ни на один курс. Пожалуйста, зарегистрируйтесь на курсы в разделе "Регистрация".</p>
          </div>
        </div>

        <!-- Teacher view with GPA and course grid -->
        <div *ngIf="isTeacher">
          <!-- GPA summary card -->
          <div class="card p-4 mb-6 rounded-lg shadow-sm">
            <div class="flex flex-wrap items-center justify-between">
              <div>
                <div class="text-sm text-tertiary mb-1">{{ 'RATINGS.CURRENT_GPA' | translate }}</div>
                <div class="flex items-baseline">
                  <span class="text-2xl font-bold text-primary-600">{{ currentGPA }}</span>
                  <span class="text-sm text-tertiary ml-2">/ 4.0</span>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm text-tertiary mb-1">{{ 'RATINGS.TOTAL_CREDITS' | translate }}</div>
                <div class="text-xl font-semibold">{{ totalCredits }}</div>
              </div>
            </div>
          </div>

          <!-- Course grid -->
          <div class="grid xl:grid-cols-3 lg:grid-cols-2 grid-cols-1 sm:grid-cols-2 gap-4">
            <div
              *ngFor="let course of courses"
              class="course-card card shadow-sm hover:shadow-md p-5 rounded-lg flex flex-col items-center justify-center"
              (click)="selectCourse(course)">
              <div class="progress-circle mb-3"
                  [ngStyle]="{
                    '--value': course.current + '%',
                    '--color': getColor(course.current)
                  }">
                <div class="progress-text font-bold">{{ course.current }}%</div>
              </div>
              <div class="text-center mb-1">
                <div class="font-medium text-sm">{{ course.title }}</div>
                <div class="text-xs text-tertiary">{{ course.code }}</div>
              </div>
              <div class="flex items-center justify-center mt-2 gap-2">
                <span class="text-xs py-1 px-2 rounded-full"
                  [ngClass]="{
                    'bg-green-100 text-green-800': course.gpa >= 3.0,
                    'bg-yellow-100 text-yellow-800': course.gpa >= 2.0 && course.gpa < 3.0,
                    'bg-red-100 text-red-800': course.gpa < 2.0
                  }">
                  {{ course.letterGrade }}
                </span>
                <span class="text-xs text-tertiary">{{ course.gpa }} GPA</span>
              </div>
            </div>
          </div>
        </div>

        <!-- GPA explanation (only for teachers) -->
        <div *ngIf="isTeacher" class="mt-8 text-sm">
          <div class="flex items-center gap-2 mb-2">
            <img src="/assets/icons/info.svg" class="size-4" alt="Info">
            <div class="font-medium">{{ 'RATINGS.WHAT_IS_GPA' | translate }}</div>
          </div>
          <div class="text-sm text-tertiary">
            {{ 'RATINGS.GPA_EXPLANATION' | translate }}
            <div class="mt-2">
              <span class="text-primary-600">💡</span> {{ 'RATINGS.GPA_EXAMPLE' | translate }}
              <div class="mt-1">
                <span class="text-primary-600">🎯</span> {{ 'RATINGS.GOOD_GPA' | translate }}
                <ul class="list-disc ml-6 mt-1">
                  <li>3.5 – 4.0: {{ 'RATINGS.EXCELLENT' | translate }}</li>
                  <li>3.0 – 3.4: {{ 'RATINGS.GOOD' | translate }}</li>
                  <li>2.0 – 2.9: {{ 'RATINGS.SATISFACTORY' | translate }}</li>
                  <li>< 2.0: {{ 'RATINGS.UNSATISFACTORY' | translate }} <span class="text-red-500">({{ 'RATINGS.ACADEMIC_WARNING' | translate }})</span></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right column - GPA scale (only for teachers) -->
      <div *ngIf="isTeacher" class="xl:w-[25%] lg:w-1/3 w-full">
        <div class="card shadow-sm rounded-lg overflow-hidden">
          <div class="p-3 border-b border-border-light">
            <div class="text-sm font-medium">{{ 'RATINGS.GRADING_SCALE' | translate }}</div>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full text-xs text-left">
              <thead class="bg-tertiary text-secondary">
                <tr>
                  <th class="px-4 py-2 border-b">{{ 'RATINGS.PERCENT' | translate }}</th>
                  <th class="px-4 py-2 border-b">{{ 'RATINGS.LETTER' | translate }}</th>
                  <th class="px-2 py-2 border-b">{{ 'RATINGS.GPA_SCALE' | translate }}</th>
                </tr>
              </thead>
              <tbody class="divide-y">
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">93–100%</td>
                  <td class="px-4 py-2">A</td>
                  <td class="px-4 py-2">4.0</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">90–92%</td>
                  <td class="px-4 py-2">A-</td>
                  <td class="px-4 py-2">3.7</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">87–89%</td>
                  <td class="px-4 py-2">B+</td>
                  <td class="px-4 py-2">3.3</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">83–86%</td>
                  <td class="px-4 py-2">B</td>
                  <td class="px-4 py-2">3.0</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">80–82%</td>
                  <td class="px-4 py-2">B-</td>
                  <td class="px-4 py-2">2.7</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">77–79%</td>
                  <td class="px-4 py-2">C+</td>
                  <td class="px-4 py-2">2.3</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">73–76%</td>
                  <td class="px-4 py-2">C</td>
                  <td class="px-4 py-2">2.0</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">70–72%</td>
                  <td class="px-4 py-2">C-</td>
                  <td class="px-4 py-2">1.7</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">67–69%</td>
                  <td class="px-4 py-2">D+</td>
                  <td class="px-4 py-2">1.3</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">65–66%</td>
                  <td class="px-4 py-2">D</td>
                  <td class="px-4 py-2">1.0</td>
                </tr>
                <tr class="hover:bg-tertiary">
                  <td class="px-4 py-2">< 65%</td>
                  <td class="px-4 py-2">F</td>
                  <td class="px-4 py-2">0.0</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Right column for students - Semester information -->
      <div *ngIf="!isTeacher" class="xl:w-[25%] lg:w-1/3 w-full">
        <div class="card shadow-sm rounded-lg overflow-hidden">
          <div class="p-3 border-b border-border-light">
            <div class="text-sm font-medium">Информация о семестре</div>
          </div>
          <div class="p-4">
            <div class="text-sm text-gray-600 dark:text-gray-400">
              <p>Итоговые оценки будут доступны после окончания семестра. Оценки выставляются на основе выполненных заданий, экзаменов и других форм контроля знаний.</p>
              <div class="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
                <div class="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-xs font-medium text-blue-800 dark:text-blue-300">Важно</span>
                </div>
                <p class="mt-1 text-xs">Следите за выполнением заданий и не пропускайте дедлайны, чтобы получить высокие оценки по курсам.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Course details view -->
  <div *ngIf="!isLoading && activeTab === 'details' && selectedCourse">
    <div class="mb-4">
      <button
        (click)="backToCourses()"
        class="flex items-center text-sm text-primary-600 hover:text-primary-700">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
        {{ 'RATINGS.BACK_TO_COURSES' | translate }}
      </button>
    </div>

    <div class="card shadow-sm rounded-lg p-5 mb-6">
      <div class="flex flex-wrap items-start justify-between">
        <div>
          <h2 class="text-lg font-semibold mb-1">{{ selectedCourse.title }}</h2>
          <div class="text-sm text-tertiary mb-3">{{ selectedCourse.code }}</div>

          <div class="flex flex-wrap gap-4 mt-2">
            <div>
              <div class="text-xs text-tertiary mb-1">{{ 'RATINGS.OVERALL_GRADE' | translate }}</div>
              <div class="text-lg font-semibold">{{ selectedCourse.current }}%</div>
            </div>
            <div>
              <div class="text-xs text-tertiary mb-1">{{ 'RATINGS.LETTER_GRADE' | translate }}</div>
              <div class="text-lg font-semibold">{{ selectedCourse.letterGrade }}</div>
            </div>
            <div>
              <div class="text-xs text-tertiary mb-1">{{ 'RATINGS.GPA_CONTRIBUTION' | translate }}</div>
              <div class="text-lg font-semibold">{{ selectedCourse.gpa }}</div>
            </div>
            <div>
              <div class="text-xs text-tertiary mb-1">{{ 'RATINGS.CREDITS' | translate }}</div>
              <div class="text-lg font-semibold">{{ selectedCourse.credits }}</div>
            </div>
          </div>
        </div>

        <div class="mt-4 sm:mt-0">
          <div class="progress-circle"
              [ngStyle]="{
                '--value': selectedCourse.current + '%',
                '--color': getColor(selectedCourse.current)
              }">
            <div class="progress-text font-bold">{{ selectedCourse.current }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Assignments list -->
    <div class="card shadow-sm rounded-lg overflow-hidden">
      <div class="p-4 border-b border-border-light">
        <div class="text-base font-medium">{{ 'RATINGS.ASSIGNMENTS' | translate }}</div>
      </div>

      <div class="assignment-list">
        <div *ngFor="let assignment of selectedCourse.assignments" class="assignment-item">
          <div class="flex-1">
            <div class="font-medium">{{ assignment.title }}</div>
            <div class="text-xs text-tertiary">{{ assignment.date | date:'mediumDate' }}</div>
          </div>
          <div class="text-sm">
            <div class="text-xs text-tertiary text-right mb-1">{{ 'RATINGS.WEIGHT' | translate }}: {{ assignment.weight }}%</div>
            <div class="assignment-score"
                [ngClass]="{
                  'score-high': assignment.score / assignment.maxScore >= 0.8,
                  'score-medium': assignment.score / assignment.maxScore >= 0.5 && assignment.score / assignment.maxScore < 0.8,
                  'score-low': assignment.score / assignment.maxScore < 0.5
                }">
              {{ assignment.score }} / {{ assignment.maxScore }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Academic history view -->
  <div *ngIf="!isLoading && activeTab === 'history' && !selectedCourse">
    <div class="semester-selector">
      <div
        *ngFor="let semester of semesters"
        class="semester-chip"
        [class.active]="selectedSemester === semester.id.toString()"
        (click)="changeSemester(semester.id.toString())">
        {{ semester.name }}
      </div>
    </div>

    <div class="grid lg:grid-cols-2 gap-6">
      <!-- GPA trend card -->
      <div class="card shadow-sm rounded-lg p-5">
        <div class="text-base font-medium mb-3">{{ 'RATINGS.GPA_TREND' | translate }}</div>
        <div class="h-64 flex items-center justify-center text-tertiary">
          <!-- Placeholder for chart -->
          <div class="text-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-tertiary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <div>{{ 'RATINGS.CHART_PLACEHOLDER' | translate }}</div>
          </div>
        </div>
      </div>

      <!-- Semester summary card -->
      <div class="card shadow-sm rounded-lg overflow-hidden">
        <div class="p-4 border-b border-border-light">
          <div class="text-base font-medium">{{ 'RATINGS.SEMESTER_SUMMARY' | translate }}</div>
        </div>

        <div class="p-4 hidden">
          <div *ngFor="let semester of semesters" class="mb-4 last:mb-0">
            <div class="flex justify-between items-center mb-1">
              <div class="font-medium">{{ semester.name }}</div>
              <div class="text-sm">{{ semester.gpa }} GPA</div>
            </div>
            <div class="w-full bg-tertiary rounded-full h-2.5">
              <div class="h-2.5 rounded-full"
                  [ngStyle]="{
                    'width': (semester.gpa / 4 * 100) + '%',
                    'background-color': getColor(semester.gpa / 4 * 100)
                  }">
              </div>
            </div>
            <div class="text-xs text-tertiary mt-1">{{ semester.credits }} {{ 'RATINGS.CREDITS' | translate }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
