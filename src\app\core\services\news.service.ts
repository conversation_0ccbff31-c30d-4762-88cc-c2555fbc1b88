import {Injectable} from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {BehaviorSubject, map, Observable, tap} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class NewsService {

  // private apiUrl = 'http://localhost:8002/api/v1/posts/';
  private myApi = 'http://localhost:8002/api/v1/'

  // Создаём BehaviorSubject для хранения данных
  private newsSubject = new BehaviorSubject<any[]>([]);
  news$ = this.newsSubject.asObservable();  // Доступ к данным как Observable

  constructor(private http: HttpClient) {}

  // Метод загрузки новостей с сервера (загружается один раз)
  // fetchNews(): Observable<any[]> {
  //   return this.http.get<any[]>(this.apiUrl).pipe(
  //     tap(news => this.newsSubject.next(news))  // Обновляем кэшированные данные
  //   );
  // }

  // Метод удаления новости
  // deletePost(id: number) {
  //   return this.http.delete(`${this.apiUrl}${id}/`).pipe(
  //     tap(() => {
  //       const updatedNews = this.newsSubject.value.filter(post => post.id !== id);
  //       this.newsSubject.next(updatedNews);  // Обновляем локальное состояние
  //     })
  //   );
  // }

  getAllPost(): Observable<any> {
    return this.http.get(this.myApi + `posts/`)
  }

  getLastPosts(count: number): Observable<any[]> {
    return this.http.get<any[]>(this.myApi + `posts/`).pipe(
      map((posts) => posts.slice(-count)) // Получаем последние count постов
    );
  }

  // deletePost(id: number): Observable<any> {
  //   return this.http.delete(this.myApi + `post/${id}/`)
  // }

  addPost(postData: FormData): Observable<any> {
    return this.http.post(this.myApi + `posts/`, postData)
  }


}

