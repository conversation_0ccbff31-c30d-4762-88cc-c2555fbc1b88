<!-- Loading indicator -->
<div *ngIf="isLoading" class="flex justify-center items-center py-8">
  <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
</div>


<!-- Search and filter section -->
<div *ngIf="!isLoading && filteredWeeks.length !== 0" class="mb-6 bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
  <div class="flex flex-col md:flex-row gap-3 mb-4">
    <!-- Search input -->
    <div class="relative flex-grow">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (ngModelChange)="applyFilters()"
        placeholder="Поиск заданий..."
        class="pl-10 pr-4 py-2 w-full border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-200"
      >
    </div>

    <!-- Type filter -->
    <div class="md:w-1/4">
      <select
        [(ngModel)]="selectedType"
        (ngModelChange)="applyFilters()"
        class="w-full border border-gray-300 dark:border-gray-600 rounded-md text-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-200"
      >
        <option value="all">Все типы</option>
        <option value="task">Задания</option>
        <option value="info">Лекции</option>
      </select>
    </div>

    <!-- Status filter (only for students) -->
    <div *ngIf="user.role != 'teacher'" class="md:w-1/4">
      <select
        [(ngModel)]="selectedStatus"
        (ngModelChange)="applyFilters()"
        class="w-full border border-gray-300 dark:border-gray-600 rounded-md text-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-200"
      >
        <option value="all">Все статусы</option>
        <option value="completed">Выполнено</option>
        <option value="not_completed">Не выполнено</option>
        <option value="under_review">На проверке</option>
      </select>
    </div>

    <!-- Week filter -->
    <div class="md:w-1/4">
      <select
        [(ngModel)]="selectedWeek"
        (ngModelChange)="applyFilters()"
        class="w-full border border-gray-300 dark:border-gray-600 rounded-md text-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-200"
      >
        <option [ngValue]="null">Все недели</option>
        <option *ngFor="let week of weeks" [ngValue]="week.id">Неделя #{{ week.week_number }}</option>
      </select>
    </div>

    <!-- Reset filters button -->
    <button
      (click)="resetFilters()"
      class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 rounded-md text-sm transition-colors duration-200"
    >
      <div class="flex items-center justify-center gap-1">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        Сбросить
      </div>
    </button>
  </div>
</div>

<!-- Weeks content -->
<div *ngIf="!isLoading && filteredWeeks.length !== 0" class="space-y-4">
  <div *ngFor="let week of filteredWeeks" class="mb-6 rounded-lg bg-white dark:bg-gray-800 p-4 shadow-sm transition-all duration-300 hover:shadow-md">
    <div class="flex justify-between items-center cursor-pointer week-header rounded-md p-2 dark:hover:bg-gray-700" (click)="toggleWeekExpansion(week.id)">
      <div class="flex items-center gap-2">
        <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="text-base font-medium text-blue-700 dark:text-blue-400">
          Неделя #{{ week.week_number }}
        </div>
      </div>

      <div class="flex items-center gap-2">
        <!-- Assignment count badge -->
        <div class="px-2 py-1 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 text-xs rounded-full">
          {{ week.assignments?.length || 0 }} элемент(ов)
        </div>

        <!-- Expand/collapse icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 text-gray-500 dark:text-gray-400 rotate-icon"
          [ngClass]="{'expanded': isWeekExpanded(week.id)}"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>

        <!-- Delete button (commented out) -->
        <!-- <div *ngIf="user.role == 'teacher'">
          <div
            (click)="deleteWeekPrompt(week.id); $event.stopPropagation();"
            class="text-red-700 cursor-pointer text-xs p-1 hover:bg-red-50 rounded-full"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </div>
        </div> -->
      </div>
    </div>

    <!-- Week content (collapsible) -->
    <div class="collapsible-content mt-3"
         [ngClass]="{'expanded': isWeekExpanded(week.id)}"
         [style.display]="isWeekExpanded(week.id) ? 'block' : 'none'">
      <div class="flex gap-2 px-2 py-1 bg-gray-50 dark:bg-gray-700 rounded-md">
        <div class="text-sm font-medium dark:text-gray-200">{{ week.title }}:</div>
        <div class="text-sm text-gray-600 dark:text-gray-300" *ngIf="week.description">
          {{ week.description }}
        </div>
      </div>

      <div class="mt-3" *ngIf="week.assignments?.length > 0">
        <ul class="space-y-2 text-xs text-gray-700 dark:text-gray-300">
          <li *ngFor="let item of week.assignments">
            <a
              [routerLink]="[
                '/thread',
                threadID,
                'assignments',
                item.assignment.id
              ]"
              class="assignment-item hover:text-blue-500 flex flex-wrap items-center hover:shadow-md duration-200 transition-all justify-between max-sm:mb-5 p-3 border border-gray-100 dark:border-gray-700 rounded-lg dark:hover:bg-gray-700"
            >
              <div class="md:w-[30%] w-full flex items-center gap-2 mb-2 md:mb-0">
                <!-- Task icon -->
                @if (item.assignment.type == 'task') {
                <div class="flex items-center justify-center w-7 h-7 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
                <div class="font-medium dark:text-gray-200">Задание:</div>
                }
                <!-- Lecture icon -->
                @else {
                <div class="flex items-center justify-center w-7 h-7 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div class="font-medium dark:text-gray-200">Лекция:</div>
                }
                <div class="truncate">{{ item.assignment.title }}</div>
              </div>

              <!-- Due date -->
              <div class="md:w-[20%] w-full flex items-center gap-2 mb-2 md:mb-0">
                <div class="flex items-center justify-center w-6 h-6 rounded-full"
                     [ngClass]="{
                       'deadline-far': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'far',
                       'deadline-approaching': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'approaching',
                       'deadline-close': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'close',
                       'deadline-overdue': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'overdue',
                       'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': item.assignment.type == 'info' || getDeadlineStatus(item.assignment.due_date) === 'none'
                     }">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div class="text-xs">
                  <!-- Show deadline for non-overdue tasks or completed overdue tasks -->
                  <span *ngIf="item.assignment.type != 'task' || getDeadlineStatus(item.assignment.due_date) !== 'overdue' || item.submission"
                       [ngClass]="{
                         'deadline-text-far': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'far',
                         'deadline-text-approaching': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'approaching',
                         'deadline-text-close': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'close',
                         'deadline-text-overdue': item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'overdue' && item.submission
                       }">
                    {{
                      item.assignment.due_date?.seconds * 1000
                        | date : "d MMMM y, HH:mm"
                    }}
                  </span>
                  <!-- Overdue message -->
                  <span *ngIf="item.assignment.type == 'task' && getDeadlineStatus(item.assignment.due_date) === 'overdue' && !item.submission"
                        class="px-1.5 py-0.5 text-xs rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 whitespace-nowrap">
                    Просрочено
                  </span>
                </div>
              </div>

              <!-- For students: Submission status and score -->
              @if (user.role != 'teacher') {
              <div class="md:w-[20%] w-full flex items-center gap-2 mb-2 md:mb-0">
                @if (item.assignment.type == 'task') {
                <!-- Completed icon -->
                <div *ngIf="item.submission" class="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <!-- Not completed icon -->
                <div *ngIf="!item.submission" class="flex items-center justify-center w-6 h-6 rounded-full bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <div class="text-xs dark:text-gray-300">
                  {{ item.submission ? "Выполнено" : "Не выполнено" }}
                </div>
                }
              </div>

              <!-- Score (for students) -->
              <div class="md:w-[15%] w-full flex items-center gap-1">
                <div *ngIf="item.assignment.type == 'task'" class="flex items-center">
                  <!-- Score display -->
                  <div [ngClass]="{
                       'text-green-600 dark:text-green-400': item.submission && item.submission.score !== undefined && getScorePercentage(item.submission.score, item.assignment.max_points) >= 80,
                       'text-yellow-600 dark:text-yellow-400': item.submission && item.submission.score !== undefined && getScorePercentage(item.submission.score, item.assignment.max_points) >= 50 && getScorePercentage(item.submission.score, item.assignment.max_points) < 80,
                       'text-red-600 dark:text-red-400': item.submission && item.submission.score !== undefined && getScorePercentage(item.submission.score, item.assignment.max_points) < 50,
                       'text-gray-500 dark:text-gray-400': !item.submission || item.submission.score === undefined
                     }">
                    <span *ngIf="!item.submission">0/{{ item.assignment.max_points }}</span>
                    <span *ngIf="item.submission && item.submission.score !== undefined">{{ item.submission.score }}/{{ item.assignment.max_points }}</span>
                    <span *ngIf="item.submission && item.submission.score === undefined">—/{{ item.assignment.max_points }}</span>
                  </div>

                  <!-- Status badge -->
                  <span *ngIf="!item.submission && getDeadlineStatus(item.assignment.due_date) !== 'overdue'"
                        class="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                    Не выполнено
                  </span>
                  <span *ngIf="!item.submission && getDeadlineStatus(item.assignment.due_date) === 'overdue'"
                        class="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                    Просрочено
                  </span>
                  <span *ngIf="item.submission && item.submission.score === undefined"
                        class="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                    На проверке
                  </span>
                  <span *ngIf="item.submission && item.submission.score !== undefined && getScorePercentage(item.submission.score, item.assignment.max_points) >= 80"
                        class="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                    Отлично
                  </span>
                  <span *ngIf="item.submission && item.submission.score !== undefined && getScorePercentage(item.submission.score, item.assignment.max_points) >= 50 && getScorePercentage(item.submission.score, item.assignment.max_points) < 80"
                        class="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
                    Хорошо
                  </span>
                  <span *ngIf="item.submission && item.submission.score !== undefined && getScorePercentage(item.submission.score, item.assignment.max_points) < 50"
                        class="ml-2 px-1.5 py-0.5 text-xs rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                    Низкий балл
                  </span>
                </div>
              </div>
              }

              <!-- For teachers: Show max points -->
              @if (user.role == 'teacher') {
              <div class="md:w-[15%] w-full flex items-center gap-1">
                <div *ngIf="item.assignment.type == 'task'" class="flex items-center">
                  <div class="text-gray-500 dark:text-gray-400 text-xs">
                    Макс. баллов: {{ item.assignment.max_points }}
                  </div>
                </div>
              </div>
              }
            </a>
          </li>
        </ul>
      </div>

      <div *ngIf="week.assignments?.length === 0" class="mt-3 text-sm text-gray-400 dark:text-gray-500 flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Нет заданий
      </div>
    </div>
  </div>
</div>

<!-- Empty state -->
<div *ngIf="!isLoading && filteredWeeks.length === 0 && weeks.length !== 0" class="empty-state flex flex-col items-center justify-center py-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
  </svg>
  <div class="text-gray-500 dark:text-gray-400 text-sm mb-2">Нет заданий, соответствующих вашим фильтрам</div>
  <button
    (click)="resetFilters()"
    class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white dark:bg-blue-600 dark:hover:bg-blue-700 rounded-md text-sm transition-colors duration-200"
  >
    Сбросить фильтры
  </button>
</div>

<!-- No weeks available -->
<div *ngIf="!isLoading && weeks.length === 0" class="empty-state flex flex-col items-center justify-center py-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
  <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
  <div class="text-gray-500 dark:text-gray-400 text-sm">
    Нет доступных недель или заданий для этого потока.
  </div>
</div>

<div
  class="fixed inset-0 shadow-2xl drop-shadow-2xl bg-black bg-opacity-40 dark:bg-opacity-60 flex items-center justify-center z-50"
  *ngIf="showDeleteModal"
>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full">
    <div class="text-lg font-medium text-red-600 dark:text-red-400 mb-4">
      Подтвердите удаление недели
    </div>
    <p class="mb-4 text-sm text-gray-600 dark:text-gray-300">
      Все задания, привязанные к этой неделе, будут
      <strong>безвозвратно удалены</strong>. Введите капчу для подтверждения.
    </p>
    <div class="mb-4">
      <label class="text-sm text-gray-700 dark:text-gray-300"
        >Введите слово <strong>УДАЛИТЬ</strong>:</label
      >
      <input
        [(ngModel)]="captchaInput"
        class="mt-1 w-full border rounded px-2 py-1 text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"
        placeholder="Удалить"
      />
    </div>

    <div class="flex justify-end space-x-2">
      <button
        (click)="cancelDelete()"
        class="px-3 py-1 bg-gray-300 text-sm rounded hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-200"
      >
        Отмена
      </button>
      <button
        (click)="confirmDelete()"
        [disabled]="captchaInput !== 'УДАЛИТЬ'"
        class="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 disabled:opacity-50"
      >
        Удалить
      </button>
    </div>
  </div>
</div>
