import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from "@angular/forms";
import { Router } from "@angular/router";
import { AuthService } from "../../core/services/auth.service";
import { MatSnackBar } from "@angular/material/snack-bar";
import { ThemeToggleComponent } from "../../shared/components/theme-toggle.component";
import { LanguageToggleComponent } from "../../shared/components/language-toggle.component";
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.css',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ThemeToggleComponent,
    LanguageToggleComponent,
    TranslateModule,
  ]
})
export class LoginComponent {
  loginForm = this.fb.group({
    username: ['', Validators.required],
    password: ['', [Validators.required, Validators.minLength(6)]]
  });

  isSubmitting = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  onSubmit() {
    this.errorMessage = '';
    if (this.loginForm.invalid) {
      this.loginForm.markAllAsTouched();
      return;
    }

    this.isSubmitting = true;

    this.authService.login(this.loginForm.value as { username: string; password: string }).subscribe({
      next: () => {
        this.router.navigate(['/']);
      },
      error: err => {
        if (err.status === 401) {
          this.snackBar.open('Неверный логин или пароль', 'Закрыть', {
            duration: 3000,
            panelClass: ['snackbar-error']
          });
        } else {
          this.snackBar.open('Произошла ошибка. Попробуйте позже.', 'Закрыть', {
            duration: 3000,
            panelClass: ['snackbar-error']
          });
        }
        this.isSubmitting = false;
      }
    });
  }
}
