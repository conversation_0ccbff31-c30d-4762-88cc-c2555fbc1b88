.progress-circle {
  --size: 100px;
  --value: 0%;
  --thickness: 10px;
  --bg: #e5e7eb;
  --color: #3b82f6;

  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  background: conic-gradient(var(--color) var(--value), var(--bg) 0);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: background 0.5s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.progress-circle::before {
  content: '';
  position: absolute;
  width: calc(var(--size) - var(--thickness) * 2);
  height: calc(var(--size) - var(--thickness) * 2);
  background-color: var(--card-bg, white);
  border-radius: 50%;
  z-index: 1;
  transition: background-color 0.3s ease;
}

.progress-text {
  position: relative;
  font-size: 1.2rem;
  font-weight: 500;
  z-index: 2;
  color: var(--text-primary, #111827);
  transition: color 0.3s ease;
}

/* Course card styles */
.course-card {
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.course-card:hover {
  transform: translateY(-3px);
}

.course-card:hover .course-card-overlay {
  opacity: 1;
}

.course-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Tab navigation */
.tab-nav {
  display: flex;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  margin-bottom: 1.5rem;
}

.tab-item {
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: var(--primary-600, #0284c7);
  border-bottom: 2px solid var(--primary-600, #0284c7);
}

.tab-item:hover:not(.active) {
  color: var(--primary-500, #0ea5e9);
}

/* Assignment list */
.assignment-list {
  border-radius: 0.5rem;
  overflow: hidden;
}

.assignment-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  transition: background-color 0.2s ease;
}

.assignment-item:last-child {
  border-bottom: none;
}

.assignment-item:hover {
  background-color: var(--bg-tertiary, #f3f4f6);
}

.assignment-score {
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  min-width: 3rem;
  text-align: center;
}

.score-high {
  background-color: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.score-medium {
  background-color: rgba(250, 204, 21, 0.1);
  color: #facc15;
}

.score-low {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Semester selector */
.semester-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.semester-chip {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--bg-tertiary, #f3f4f6);
}

.semester-chip.active {
  background-color: var(--primary-600, #0284c7);
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .progress-circle {
    --size: 80px;
    --thickness: 8px;
  }

  .progress-text {
    font-size: 1rem;
  }

  .tab-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}
