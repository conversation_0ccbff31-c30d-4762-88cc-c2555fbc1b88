<!-- Breadcrumbs with loading state -->
<div *ngIf="!breadcrumbsLoaded" class="flex items-center space-x-2 mb-4 h-6">
  <div class="animate-pulse bg-gray-200 dark:bg-gray-700 h-4 w-16 rounded"></div>
  <div class="text-gray-400">/</div>
  <div class="animate-pulse bg-gray-200 dark:bg-gray-700 h-4 w-24 rounded"></div>
  <div class="text-gray-400">/</div>
  <div class="animate-pulse bg-gray-200 dark:bg-gray-700 h-4 w-32 rounded"></div>
</div>
<app-breadcrumbs *ngIf="breadcrumbsLoaded" [items]="breadcrumbs"/>
<div class="max-w-screen-xl" *ngIf="assignment">
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-6 mb-6 transition-all duration-300">
    <div class="flex justify-between items-start">
      <div>
        <h1 class="text-2xl font-medium text-blue-800 dark:text-blue-400">{{ assignment.title }}</h1>

        <!-- Assignment type badge -->
        <div class="mt-2 flex items-center">
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                [ngClass]="{'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300': assignment.type === 'info',
                           'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': assignment.type === 'task'}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path *ngIf="assignment.type === 'info'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              <path *ngIf="assignment.type === 'task'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            {{ assignment.type === 'info' ? 'Информация' : 'Задание' }}
          </span>

          <!-- Deadline display for tasks -->
          @if (assignment.type == 'task') {
            <div class="ml-3 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-500 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-sm font-medium text-red-600 dark:text-red-400">
                {{ assignment.due_date?.seconds * 1000 | date:'d MMMM y, HH:mm' }}
              </span>

              <!-- Deadline status from studentAssignmentDetails -->
              <div *ngIf="studentAssignmentDetails?.deadline_status" class="ml-2">
                <span class="text-xs px-2 py-0.5 rounded-full"
                      [ngClass]="{'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': !studentAssignmentDetails.deadline_status.is_overdue,
                                 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': studentAssignmentDetails.deadline_status.is_overdue}">
                  {{ studentAssignmentDetails.deadline_status.status_text }}
                </span>
              </div>
            </div>
          }
        </div>
      </div>

      <!-- Only show action menu button for teachers and admins -->
      <div class="relative" *ngIf="currentUser?.role === 'teacher' || currentUser?.role === 'admin'">
        <button (click)="toggleActionMenu()"
                class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none transition-colors duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600 dark:text-gray-300" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
          </svg>
        </button>

        <!-- Action Menu -->
        <div *ngIf="showActionMenu"
             class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-10 transform origin-top-right transition-all duration-200">
          <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
            <button (click)="openEditDialog()"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                    role="menuitem">
              <svg xmlns="http://www.w3.org/2000/svg" class="inline-block h-4 w-4 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
              Редактировать
            </button>
            <button (click)="openDeleteDialog()"
                    class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
                    role="menuitem">
              <svg xmlns="http://www.w3.org/2000/svg" class="inline-block h-4 w-4 mr-2 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Удалить
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Assignment description -->
    <div class="mt-6 text-sm text-gray-700 dark:text-gray-300 prose prose-sm max-w-none" [innerHTML]="assignment.description"></div>
  </div>

  <!-- Assignment attachments -->
  <div *ngIf="assignment?.attachments && assignment.attachments.length > 0"
       class="mt-4 p-5 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
    <div class="flex items-center mb-3">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
      </svg>
      <div class="text-sm font-medium text-gray-800 dark:text-gray-200">Материалы задания</div>
    </div>
    <div class="flex flex-wrap gap-2">
      <a *ngFor="let attachment of assignment.attachments"
         [href]="getDownloadUrl(attachment.file_url)"
         target="_blank"
         class="flex items-center px-3 py-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-gray-600 hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-200 file-attachment shadow-sm hover:shadow">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        <span class="truncate max-w-xs">{{ attachment.file_url.split('/').pop() }}</span>
      </a>
    </div>
  </div>
  @if (assignment.type == 'task') {
    <div class="mt-4">
      <!-- Loading indicator -->
      <div *ngIf="isLoadingSubmissions" class="flex flex-col items-center justify-center py-6">
        <div class="spinner rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"></div>
        <p class="text-sm text-gray-500 dark:text-gray-400">Загрузка ответов...</p>
      </div>

      <!-- No submissions message -->
      <div *ngIf="!isLoadingSubmissions && (!submissions || submissions.length === 0) && currentUser?.role === 'teacher'"
           class="mt-4 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 text-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400 dark:text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <p class="text-sm text-gray-500 dark:text-gray-400">Пока нет ответов на это задание.</p>
      </div>

      <!-- Submissions section -->
      <div *ngIf="!isLoadingSubmissions && submissions && submissions.length > 0" class="mt-6 submission-section">
        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <div class="text-base font-medium text-gray-900 dark:text-gray-100">Ответы на задание</div>

              <!-- Only show submission count to teachers and admins -->
              <ng-container *ngIf="currentUser?.role === 'teacher' || currentUser?.role === 'admin'">
                <span class="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  {{ submissions.length }}
                </span>

                <!-- Show filtered count if filtering is active -->
                <span *ngIf="isFiltering" class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                  (показано: {{ filteredSubmissions.length }})
                </span>

                <!-- Show average score if available -->
                <ng-container *ngIf="calculateAverageScore() !== null && assignment?.max_points">
                  <span class="ml-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    Средний балл: {{ calculateAverageScore() | number:'1.1-1' }}/{{ assignment.max_points }}
                  </span>
                </ng-container>
              </ng-container>
            </div>

            <!-- Reset filters button - only visible when filtering is active and for teachers/admins -->
            <button
              *ngIf="isFiltering && (currentUser?.role === 'teacher' || currentUser?.role === 'admin')"
              (click)="resetFilters()"
              class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              Сбросить фильтры
            </button>
          </div>

          <!-- Search and filter controls - only visible to teachers and admins -->
          <div *ngIf="currentUser?.role === 'teacher' || currentUser?.role === 'admin'" class="mb-3 flex flex-wrap gap-2">
            <!-- Search input -->
            <div class="relative flex-1 min-w-[200px]">
              <div class="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                <svg class="w-3 h-3 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                </svg>
              </div>
              <input
                type="text"
                [(ngModel)]="searchTerm"
                (input)="onSearchInput()"
                class="block w-full py-1 px-2 pl-7 text-xs text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Поиск по имени или комментарию">
              <button
                *ngIf="searchTerm"
                (click)="clearSearch()"
                class="absolute inset-y-0 right-0 flex items-center pr-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <!-- Grade filter -->
            <div class="w-auto">
              <select
                [(ngModel)]="selectedGradeFilter"
                (change)="onGradeFilterChange(selectedGradeFilter)"
                class="block w-full py-1 px-2 text-xs text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500">
                <option value="all">Все ответы</option>
                <option value="graded">Оцененные</option>
                <option value="ungraded">Неоцененные</option>
              </select>
            </div>

            <!-- Score filter -->
            <div class="w-auto">
              <select
                [(ngModel)]="selectedScoreFilter"
                (change)="onScoreFilterChange(selectedScoreFilter)"
                class="block w-full py-1 px-2 text-xs text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500">
                <option value="all">Все оценки</option>
                <option value="high">Высокие (≥80%)</option>
                <option value="medium">Средние (50-80%)</option>
                <option value="low">Низкие (≤50%)</option>
              </select>
            </div>
          </div>

          <!-- User's own submission (using studentAssignmentDetails) -->
          <div *ngIf="hasSubmitted" class="bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-3 submission-card shadow-sm hover:shadow transition-all duration-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-800">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-blue-600 dark:text-blue-300" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div class="ml-2">
                  <p class="text-xs font-medium text-blue-800 dark:text-blue-300">Ваш ответ</p>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    <span *ngIf="studentSubmission?.submitted_at?.seconds">
                      {{ studentSubmission.submitted_at.seconds * 1000 | date:'d MMM, HH:mm' }}
                    </span>
                    <span *ngIf="studentSubmission && !studentSubmission.submitted_at?.seconds">
                      {{ studentSubmission.submitted_at | date:'d MMM, HH:mm' }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- Student's grade (from studentAssignmentDetails) -->
              <div *ngIf="studentSubmission && studentSubmission.score !== undefined" class="flex items-center">
                <span class="text-xs px-2 py-0.5 rounded-full font-medium"
                      [ngClass]="{
                        'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': studentSubmission.score >= 70,
                        'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300': studentSubmission.score >= 50 && studentSubmission.score < 70,
                        'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': studentSubmission.score < 50
                      }">
                  {{ studentSubmission.score }}/{{ assignment.max_points }}
                </span>
              </div>
            </div>

            <!-- Loading indicator for student details -->
            <div *ngIf="isLoadingStudentDetails" class="flex justify-center py-2">
              <div class="spinner rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>

            <!-- Student submission details -->
            <div *ngIf="!isLoadingStudentDetails && studentSubmission" class="mt-2">
              <!-- Student's comment -->
              <div *ngIf="studentSubmission.comment" class="mt-2 mb-2">
                <div class="flex items-center mb-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                  </svg>
                  <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Ответ на задание:</div>
                </div>
                <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-100 dark:border-gray-600">
                  <div class="text-xs text-gray-700 dark:text-gray-300" [innerHTML]="studentSubmission.comment"></div>
                </div>
              </div>

              <!-- File attachments if any -->
              <div *ngIf="studentSubmission.file_urls && studentSubmission.file_urls.length > 0" class="mt-2">
                <div class="flex items-center mb-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                  <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Ваши файлы:</div>
                </div>
                <div class="flex flex-wrap gap-1.5">
                  <a *ngFor="let fileUrl of studentSubmission.file_urls"
                     [href]="getDownloadUrl(fileUrl)"
                     target="_blank"
                     class="flex items-center px-2 py-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md text-xs text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-gray-600 hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-200 file-attachment shadow-sm hover:shadow">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    <span class="truncate max-w-xs">{{ fileUrl.split('/').pop() }}</span>
                  </a>
                </div>
              </div>

              <!-- Teacher feedback if graded -->
              <div *ngIf="studentSubmission.feedback" class="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-md">
                <div class="flex items-center mb-1">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-blue-600 dark:text-blue-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                  <div class="text-xs font-medium text-blue-800 dark:text-blue-300">{{ 'ASSIGNMENTS.teacher_feedback' | translate }}</div>
                </div>
                <div class="text-xs text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 p-2 rounded border border-blue-50 dark:border-blue-900">
                  {{ studentSubmission.feedback }}
                </div>
              </div>
            </div>
          </div>

          <!-- Other submissions (for teachers/admins) -->
          <div *ngIf="currentUser?.role === 'teacher' || currentUser?.role === 'admin'" class="space-y-5">
            <!-- No results message when filtering - only visible to teachers and admins -->
            <div *ngIf="isFiltering && filteredSubmissions.length === 0 && (currentUser?.role === 'teacher' || currentUser?.role === 'admin')" class="text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto text-gray-400 dark:text-gray-500 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              <p class="text-sm text-gray-500 dark:text-gray-400">Нет результатов, соответствующих вашим фильтрам.</p>
              <button
                (click)="resetFilters()"
                class="mt-3 text-xs bg-blue-600 py-1.5 px-3 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 shadow-sm">
                Сбросить фильтры
              </button>
            </div>

            <div *ngFor="let submission of filteredSubmissions"
                 class="border border-gray-200 dark:border-gray-700 rounded-lg submission-card bg-white dark:bg-gray-800 shadow-sm hover:shadow transition-all duration-200 cursor-pointer"
                 [ngClass]="{'filtered-item': isFiltering}"
                 (click)="toggleSubmissionExpanded(submission.id)">
              <!-- Compact view (always visible) -->
              <div class="p-3">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <!-- User name and badge -->
                    <div class="flex-shrink-0">
                      <div class="flex items-center justify-center w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-600 dark:text-gray-300" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                        </svg>
                      </div>
                    </div>

                    <div class="ml-2">
                      <!-- Display user name -->
                      <p class="text-xs font-medium text-gray-900 dark:text-gray-100">
                        <span *ngIf="submission.user && (submission.user.name || submission.user.surname)">
                          {{ submission.user.name }} {{ submission.user.surname }}
                        </span>
                        <span *ngIf="!submission.user || (!submission.user.name && !submission.user.surname)">
                          ID: {{ submission.user_id }}
                        </span>
                      </p>

                      <!-- Submission date -->
                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        <span *ngIf="submission.submitted_at?.seconds">
                          {{ submission.submitted_at.seconds * 1000 | date:'d MMM, HH:mm' }}
                        </span>
                        <span *ngIf="!submission.submitted_at?.seconds">
                          {{ submission.submitted_at | date:'d MMM, HH:mm' }}
                        </span>
                      </div>
                    </div>

                    <!-- File indicator - only show count to teachers -->
                    <div *ngIf="submission.file_urls && submission.file_urls.length > 0" class="ml-2">
                      <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                        </svg>
                        <ng-container *ngIf="currentUser?.role === 'teacher' || currentUser?.role === 'admin'">
                          {{ submission.file_urls.length }}
                        </ng-container>
                        <ng-container *ngIf="currentUser?.role !== 'teacher' && currentUser?.role !== 'admin'">
                          Файлы
                        </ng-container>
                      </span>
                    </div>
                  </div>

                  <div class="flex items-center">
                    <!-- Score badge if graded -->
                    <div *ngIf="submission.score !== undefined" class="flex items-center mr-2">
                      <span class="text-xs px-2 py-0.5 rounded-full font-medium"
                            [ngClass]="{
                              'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300': submission.score >= 70,
                              'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300': submission.score >= 50 && submission.score < 70,
                              'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300': submission.score < 50
                            }">
                        {{ submission.score }}/{{ assignment.max_points }}
                      </span>
                    </div>

                    <!-- Expand/collapse indicator -->
                    <button
                      (click)="toggleSubmissionExpanded(submission.id, $event)"
                      class="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none transition-colors duration-200">
                      <svg *ngIf="!expandedSubmissions[submission.id]" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                      </svg>
                      <svg *ngIf="expandedSubmissions[submission.id]" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Expanded view (only visible when expanded) -->
              <div *ngIf="expandedSubmissions[submission.id]" class="border-t border-gray-200 dark:border-gray-700 p-3" (click)="$event.stopPropagation()">
                <div class="flex flex-col space-y-3">
                  <!-- File attachments if any -->
                  <div *ngIf="submission.file_urls && submission.file_urls.length > 0">
                    <div class="flex items-center mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                      </svg>
                      <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Файлы:</div>
                    </div>
                    <div class="flex flex-wrap gap-1.5" (click)="$event.stopPropagation()">
                      <a *ngFor="let fileUrl of submission.file_urls"
                         [href]="getDownloadUrl(fileUrl)"
                         target="_blank"
                         (click)="$event.stopPropagation()"
                         class="flex items-center px-2 py-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md text-xs text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-gray-600 hover:border-blue-300 dark:hover:border-blue-500 transition-all duration-200 file-attachment shadow-sm hover:shadow">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                        <span class="truncate max-w-xs">{{ fileUrl.split('/').pop() }}</span>
                      </a>
                    </div>
                  </div>

                  <!-- Comment section - only shown when expanded and not in grading mode -->
                  <div *ngIf="submission.comment && !showGradingForm[submission.id]">
                    <div class="flex items-center mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-500 dark:text-gray-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                      </svg>
                      <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Комментарий:</div>
                    </div>
                    <div class="p-2 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-100 dark:border-gray-600">
                      <div class="text-xs text-gray-700 dark:text-gray-300" [innerHTML]="submission.comment"></div>
                    </div>
                  </div>

                  <!-- Teacher feedback if graded -->
                  <div *ngIf="submission.feedback && !showGradingForm[submission.id]">
                    <div class="flex items-center mb-1">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-blue-600 dark:text-blue-400 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                      </svg>
                      <div class="text-xs font-medium text-blue-800 dark:text-blue-300">{{ 'ASSIGNMENTS.teacher_feedback' | translate }}</div>
                    </div>
                    <div class="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-100 dark:border-blue-800">
                      <div class="text-xs text-gray-700 dark:text-gray-300">{{ submission.feedback }}</div>
                    </div>
                  </div>

                  <!-- Grade button or grading form -->
                  <div *ngIf="currentUser?.role === 'teacher' || currentUser?.role === 'admin'">
                    <div class="flex space-x-2">
                      <button
                        *ngIf="!showGradingForm[submission.id]"
                        (click)="toggleGradingForm(submission.id); $event.stopPropagation()"
                        class="flex items-center text-xs bg-blue-600 py-1 px-2 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 shadow-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        {{ submission.score !== undefined ? ('ASSIGNMENTS.edit_grade' | translate) : ('ASSIGNMENTS.grade_work' | translate) }}
                      </button>

                      <button
                        *ngIf="showGradingForm[submission.id]"
                        (click)="toggleGradingForm(submission.id); $event.stopPropagation()"
                        class="flex items-center text-xs bg-gray-200 dark:bg-gray-700 py-1 px-2 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200 shadow-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Отмена
                      </button>
                    </div>

                    <!-- Grading form -->
                    <div *ngIf="showGradingForm[submission.id]" (click)="$event.stopPropagation()" class="mt-2">
                      <app-grade-submission
                        [submission]="submission"
                        [maxPoints]="assignment.max_points"
                        (gradingComplete)="onGradingComplete($event)">
                      </app-grade-submission>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Submit assignment section -->
      <div class="mt-3 space-y-2">
        <!-- Show deadline passed message -->
        <div *ngIf="isDeadlinePassed && !hasSubmitted" class="flex items-center text-sm text-red-600 p-3 bg-red-50 rounded-lg">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v4a1 1 0 102 0V7zm-1 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
          </svg>
          Срок сдачи задания истек
        </div>

        <!-- Show button only if user hasn't submitted yet and deadline hasn't passed -->
        <div *ngIf="currentUser.role == 'student'">
          <button *ngIf="!addTask && !hasSubmitted && !isDeadlinePassed"
                  (click)="toggleTask()"
                  class="text-xs bg-blue-600 py-1.5 px-3 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 shadow-sm">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
              </svg>
              Начать задание
            </div>
          </button>
        </div>


        <!-- Show completed status if user has submitted -->
        <div *ngIf="!addTask && hasSubmitted" class="flex items-center text-sm text-green-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          Задание выполнено
        </div>

        <!-- Submission form -->
        <div *ngIf="addTask">
          <app-add-submission
            [assignmentId]="assignmentId"
            [assignment]="assignment"
            (submissionComplete)="onSubmissionComplete()">
          </app-add-submission>
        </div>
      </div>
    </div>
  }
</div>

<!-- Click outside directive to close the action menu -->
<div *ngIf="showActionMenu" class="fixed inset-0 z-0" (click)="closeActionMenu()"></div>


