import { Component, OnInit } from '@angular/core';
import { DateAdapter } from '@angular/material/core';
import { ExamService, ExamDisplay } from '../../core/services/exam.service';
import { AuthService } from '../../core/services/auth.service';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-exam-schedule',
  templateUrl: './exam-schedule.component.html',
  styleUrl: './exam-schedule.component.css'
})
export class ExamScheduleComponent implements OnInit {
  breadcrumbs: { label: string, url?: string }[] = [];

  // Loading state
  isLoading: boolean = false;

  // Error state
  loadError: string | null = null;

  // User role
  isTeacher: boolean = false;

  // Exam schedule data
  examSchedule: ExamDisplay[] = [];

  // Filter options
  searchTerm: string = '';
  selectedDate: Date | null = null;

  // Filtered exams
  filteredExams: ExamDisplay[] = [];

  // Display columns
  displayedColumns: string[] = ['course', 'date', 'location', 'type', 'actions'];

  constructor(
    private dateAdapter: DateAdapter<Date>,
    private examService: ExamService,
    private authService: AuthService
  ) {
    // Check if user is a teacher
    const currentUser = this.authService.getCurrentUser();
    this.isTeacher = currentUser?.role === 'teacher';
  }

  ngOnInit() {
    this.breadcrumbs = [
      { label: 'Главная', url: '/' },
      { label: 'Расписание экзаменов' },
    ];

    // Set the locale for the date adapter
    this.dateAdapter.setLocale('ru-RU');

    // Load exam schedule data
    this.loadExamSchedule();
  }

  // Load exam schedule data based on user role
  loadExamSchedule() {
    this.isLoading = true;
    this.loadError = null;

    // Call the appropriate service method based on user role
    const examObservable = this.isTeacher
      ? this.examService.getTeacherThreads()
      : this.examService.getStudentThreads();

    examObservable
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (exams) => {
          if (exams && exams.length > 0) {
            this.examSchedule = exams;
            this.filterExams();
          } else {
            // If no exams are returned, load fallback data
            this.loadFallbackData();
          }
        },
        error: (error) => {
          console.error('Error loading exam schedule:', error);
          this.loadError = 'Failed to load exam schedule. Please try again later.';
          // Load fallback data if API fails
          this.loadFallbackData();
        }
      });
  }

  // Load fallback data if API fails
  loadFallbackData() {
    this.examSchedule = [
      {
        id: 1,
        thread_id: 1,
        course_title: 'Математический анализ',
        course_id: 101,
        semester_name: 'Осень 2024',
        semester_id: 1,
        teacher_name: 'Иванов А.П.',
        teacher_id: 201,
        stream_title: 'MATH101-1',
        schedules: [
          { day: 1, startTime: '09:00:00', endTime: '10:30:00' },
          { day: 3, startTime: '09:00:00', endTime: '10:30:00' }
        ],
        exam_status: 'not_scheduled'
      },
      {
        id: 2,
        thread_id: 2,
        course_title: 'Программирование на Python',
        course_id: 102,
        semester_name: 'Осень 2024',
        semester_id: 1,
        teacher_name: 'Петров С.В.',
        teacher_id: 202,
        stream_title: 'CS102-1',
        schedules: [
          { day: 2, startTime: '13:00:00', endTime: '14:30:00' },
          { day: 4, startTime: '13:00:00', endTime: '14:30:00' }
        ],
        exam_status: 'not_scheduled'
      },
      {
        id: 3,
        thread_id: 3,
        course_title: 'Базы данных',
        course_id: 103,
        semester_name: 'Осень 2024',
        semester_id: 1,
        teacher_name: 'Сидоров И.К.',
        teacher_id: 203,
        stream_title: 'CS201-1',
        schedules: [
          { day: 2, startTime: '10:00:00', endTime: '11:30:00' },
          { day: 5, startTime: '10:00:00', endTime: '11:30:00' }
        ],
        exam_status: 'not_scheduled'
      },
      {
        id: 4,
        thread_id: 4,
        course_title: 'Английский язык',
        course_id: 104,
        semester_name: 'Осень 2024',
        semester_id: 1,
        teacher_name: 'Смирнова Е.А.',
        teacher_id: 204,
        stream_title: 'ENG101-1',
        schedules: [
          { day: 3, startTime: '14:00:00', endTime: '15:30:00' }
        ],
        exam_status: 'not_scheduled'
      },
      {
        id: 5,
        thread_id: 5,
        course_title: 'Физика',
        course_id: 105,
        semester_name: 'Осень 2024',
        semester_id: 1,
        teacher_name: 'Козлов Д.М.',
        teacher_id: 205,
        stream_title: 'PHYS101-1',
        schedules: [
          { day: 1, startTime: '13:00:00', endTime: '14:30:00' },
          { day: 4, startTime: '15:00:00', endTime: '16:30:00' }
        ],
        exam_status: 'not_scheduled'
      }
    ];

    this.filterExams();
  }

  // Filter exams based on search term
  filterExams() {
    if (!this.examSchedule || this.examSchedule.length === 0) {
      this.filteredExams = [];
      return;
    }

    this.filteredExams = this.examSchedule.filter(exam => {
      // Filter by search term
      const matchesSearch = !this.searchTerm ||
        exam.course_title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        exam.stream_title.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        exam.teacher_name.toLowerCase().includes(this.searchTerm.toLowerCase());

      return matchesSearch;
    });

    // Sort by course title
    this.filteredExams.sort((a, b) => a.course_title.localeCompare(b.course_title));
  }

  // Reset filters
  resetFilters() {
    this.searchTerm = '';
    this.selectedDate = null;
    this.filterExams();
  }

  // Format schedule for display
  formatSchedule(schedules: { day: number, startTime: string, endTime: string }[] | null): string {
    if (!schedules || schedules.length === 0) {
      return 'Расписание не указано';
    }

    return schedules.map(schedule => {
      const day = this.examService.getDayOfWeekName(schedule.day);
      return `${day}, ${this.formatTimeOnly(schedule.startTime)} - ${this.formatTimeOnly(schedule.endTime)}`;
    }).join('; ');
  }

  // Format time only (HH:MM)
  formatTimeOnly(timeString: string): string {
    return timeString.substring(0, 5);
  }

  // Get exam status text
  getExamStatusText(status: string): string {
    return status === 'scheduled' ? 'Запланирован' : 'Не запланирован';
  }

  // Get exam status class
  getExamStatusClass(status: string): string {
    return status === 'scheduled' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
  }

  // Handle date selection
  onDateSelected(event: any) {
    this.selectedDate = event;
    this.filterExams();
  }

  // Clear selected date
  clearSelectedDate() {
    this.selectedDate = null;
    this.filterExams();
  }
}
