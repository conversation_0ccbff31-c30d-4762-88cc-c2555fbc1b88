import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSnackBar } from '@angular/material/snack-bar';
import { forkJoin, Observable } from 'rxjs';
import { ThreadService } from '../../../core/services/thread.service';
import { AttendanceService, AttendanceStatus, AttendanceRecord } from '../../../core/services/attendance.service';
import { TranslateService } from '@ngx-translate/core';

interface Student {
  id: number;
  name: string;
  surname: string;
  attendance?: {
    id?: number;
    status: AttendanceStatus;
    reason?: string;
  };
}

@Component({
  selector: 'app-set-attendance',
  templateUrl: './set-attendance.component.html',
  styleUrl: './set-attendance.component.css'
})
export class SetAttendanceComponent implements OnInit {
  @Input() threadID: number | null = null;

  // Form for date selection
  dateForm: FormGroup;

  // Students in the thread
  students: Student[] = [];

  // Attendance records for the selected date
  attendanceRecords: AttendanceRecord[] = [];

  // Loading states
  isLoadingStudents: boolean = false;
  isLoadingAttendance: boolean = false;
  isSaving: boolean = false;

  // Error message
  errorMessage: string | null = null;

  // Attendance status enum for template
  attendanceStatus = AttendanceStatus;

  // Reason form for excused absences
  reasonForms: { [key: number]: FormGroup } = {};

  constructor(
    private fb: FormBuilder,
    private threadService: ThreadService,
    private attendanceService: AttendanceService,
    private snackBar: MatSnackBar,
    private translate: TranslateService
  ) {
    // Initialize date form with today's date
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]

    this.dateForm = this.fb.group({
      attendanceDate: [formattedDate, Validators.required]
    });
  }

  ngOnInit(): void {
    if (this.threadID) {
      this.loadStudents();
      this.loadAttendance();
    }

    // Listen for date changes
    this.dateForm.get('attendanceDate')?.valueChanges.subscribe(() => {
      this.loadAttendance();
    });
  }

  /**
   * Load students from the thread
   */
  loadStudents(): void {
    if (!this.threadID) return;

    this.isLoadingStudents = true;
    this.threadService.getThreadMembers(this.threadID).subscribe({
      next: (data: any) => {
        if (data && data.members) {
          this.students = data.members.map((member: any) => ({
            id: member.id,
            name: member.name || member.first_name || '',
            surname: member.surname || member.last_name || '',
            attendance: {
              status: AttendanceStatus.UNMARKED
            }
          }));

          // Initialize reason forms for each student
          this.students.forEach(student => {
            this.reasonForms[student.id] = this.fb.group({
              reason: ['']
            });
          });

          this.loadAttendance();
        }
        this.isLoadingStudents = false;
      },
      error: (err) => {
        console.error('Error loading students:', err);
        this.errorMessage = this.translate.instant('ATTENDANCE.ERROR_LOADING_STUDENTS');
        this.isLoadingStudents = false;
        this.showSnackBar('ATTENDANCE.ERROR_LOADING_STUDENTS');
      }
    });
  }

  /**
   * Load attendance records for the selected date
   */
  loadAttendance(): void {
    if (!this.threadID) return;

    const dateValue = this.dateForm.get('attendanceDate')?.value;
    if (!dateValue) return;

    // The date value from the form is already in YYYY-MM-DD format
    // We can use it directly if it's a string, or format it if it's a Date object
    const formattedDate = typeof dateValue === 'string'
      ? dateValue
      : this.attendanceService.formatDateForApi(dateValue);

    this.isLoadingAttendance = true;
    this.attendanceService.getAttendanceRecords(this.threadID, formattedDate).subscribe({
      next: (records: AttendanceRecord[]) => {
        this.attendanceRecords = records;

        // Update student attendance status
        this.students.forEach(student => {
          const record = records.find(r => r.user_id === student.id);
          if (record) {
            student.attendance = {
              id: record.id,
              status: record.status,
              reason: record.reason
            };

            // Update reason form
            if (record.reason && this.reasonForms[student.id]) {
              this.reasonForms[student.id].patchValue({ reason: record.reason });
            }
          } else {
            student.attendance = {
              status: AttendanceStatus.UNMARKED
            };

            // Reset reason form
            if (this.reasonForms[student.id]) {
              this.reasonForms[student.id].patchValue({ reason: '' });
            }
          }
        });

        this.isLoadingAttendance = false;
      },
      error: (err) => {
        console.error('Error loading attendance:', err);
        this.errorMessage = this.translate.instant('ATTENDANCE.ERROR_LOADING_ATTENDANCE');
        this.isLoadingAttendance = false;
        this.showSnackBar('ATTENDANCE.ERROR_LOADING_ATTENDANCE');
      }
    });
  }

  /**
   * Update attendance status for a student
   */
  updateAttendance(student: Student, status: AttendanceStatus): void {
    if (!this.threadID) return;

    const dateValue = this.dateForm.get('attendanceDate')?.value;
    if (!dateValue) return;

    // The date value from the form is already in YYYY-MM-DD format
    // We can use it directly if it's a string, or format it if it's a Date object
    const formattedDate = typeof dateValue === 'string'
      ? dateValue
      : this.attendanceService.formatDateForApi(dateValue);
    const reason = status === AttendanceStatus.EXCUSED ? this.reasonForms[student.id].get('reason')?.value : '';

    this.isSaving = true;

    // If there's an existing record, update it
    if (student.attendance?.id) {
      this.attendanceService.updateAttendanceRecord(student.attendance.id, {
        status,
        reason
      }).subscribe({
        next: (updatedRecord) => {
          student.attendance = {
            id: updatedRecord.id,
            status: updatedRecord.status,
            reason: updatedRecord.reason
          };
          this.isSaving = false;
          this.showSnackBar('ATTENDANCE.ATTENDANCE_UPDATED');
        },
        error: (err) => {
          console.error('Error updating attendance:', err);
          this.errorMessage = this.translate.instant('ATTENDANCE.ERROR_UPDATING_ATTENDANCE');
          this.isSaving = false;
          this.showSnackBar('ATTENDANCE.ERROR_UPDATING_ATTENDANCE');
        }
      });
    }
    // Otherwise create a new record
    else {
      const newRecord: AttendanceRecord = {
        thread_id: this.threadID,
        user_id: student.id,
        attendance_date: formattedDate,
        status,
        reason
      };

      this.attendanceService.createAttendanceRecord(newRecord).subscribe({
        next: (createdRecord) => {
          student.attendance = {
            id: createdRecord.id,
            status: createdRecord.status,
            reason: createdRecord.reason
          };
          this.isSaving = false;
          this.showSnackBar('ATTENDANCE.ATTENDANCE_RECORDED');
        },
        error: (err) => {
          console.error('Error creating attendance record:', err);
          this.errorMessage = this.translate.instant('ATTENDANCE.ERROR_RECORDING_ATTENDANCE');
          this.isSaving = false;
          this.showSnackBar('ATTENDANCE.ERROR_RECORDING_ATTENDANCE');
        }
      });
    }
  }

  /**
   * Save all attendance records at once
   */
  saveAllAttendance(): void {
    if (!this.threadID) return;

    const dateValue = this.dateForm.get('attendanceDate')?.value;
    if (!dateValue) return;

    // The date value from the form is already in YYYY-MM-DD format
    // We can use it directly if it's a string, or format it if it's a Date object
    const formattedDate = typeof dateValue === 'string'
      ? dateValue
      : this.attendanceService.formatDateForApi(dateValue);

    this.isSaving = true;

    // Create observables for all updates
    const updateObservables: Observable<any>[] = [];

    this.students.forEach(student => {
      const status = student.attendance?.status || AttendanceStatus.UNMARKED;
      const reason = status === AttendanceStatus.EXCUSED ? this.reasonForms[student.id].get('reason')?.value : '';

      if (student.attendance?.id) {
        // Update existing record
        updateObservables.push(
          this.attendanceService.updateAttendanceRecord(student.attendance.id, { status, reason })
        );
      } else {
        // Create new record
        const newRecord: AttendanceRecord = {
          thread_id: this.threadID!,
          user_id: student.id,
          attendance_date: formattedDate,
          status,
          reason
        };

        updateObservables.push(
          this.attendanceService.createAttendanceRecord(newRecord)
        );
      }
    });

    // Execute all updates in parallel
    forkJoin(updateObservables).subscribe({
      next: (results) => {
        this.isSaving = false;
        this.showSnackBar('ATTENDANCE.ALL_ATTENDANCE_SAVED');
        this.loadAttendance(); // Refresh data
      },
      error: (err) => {
        console.error('Error saving attendance records:', err);
        this.errorMessage = this.translate.instant('ATTENDANCE.ERROR_SAVING_ATTENDANCE');
        this.isSaving = false;
        this.showSnackBar('ATTENDANCE.ERROR_SAVING_ATTENDANCE');
      }
    });
  }

  /**
   * Show a snackbar message
   */
  private showSnackBar(messageKey: string): void {
    const message = this.translate.instant(messageKey);
    const closeText = this.translate.instant('COMMON.cancel');

    this.snackBar.open(message, closeText, {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    });
  }

  /**
   * Get status label based on status code
   */
  getStatusLabel(status: AttendanceStatus): string {
    switch (status) {
      case AttendanceStatus.UNSPECIFIED:
        return 'ATTENDANCE.UNSPECIFIED';
      case AttendanceStatus.UNMARKED:
        return 'ATTENDANCE.UNMARKED';
      case AttendanceStatus.PRESENT:
        return 'ATTENDANCE.PRESENT';
      case AttendanceStatus.ABSENT:
        return 'ATTENDANCE.ABSENT';
      case AttendanceStatus.EXCUSED:
        return 'ATTENDANCE.EXCUSED';
      default:
        return 'ATTENDANCE.UNKNOWN';
    }
  }
}
