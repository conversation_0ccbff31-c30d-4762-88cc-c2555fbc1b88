mat-form-field {
  font-size: 13px !important;
  margin: 0 !important;
}

.mat-form-field-wrapper {
  padding-bottom: 0 !important;
}

.mat-form-field-infix {
  padding: 0.3em 0 !important;
}

.mat-input-element {
  font-size: 13px !important;
  padding: 6px 8px !important;
}


.snackbar-success {
  background-color: #4caf50;
  color: white;
  font-size: 14px;
}

.snackbar-error {
  background-color: #f44336;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Dark mode spinner */
.dark .spinner {
  border-color: transparent;
  border-bottom-color: #3b82f6; /* blue-500 */
}
