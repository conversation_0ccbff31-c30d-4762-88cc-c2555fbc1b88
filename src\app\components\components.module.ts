import {NgModule} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, CommonModule, NgClass, NgFor<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SlicePipe} from '@angular/common';
import {NgVarDirective} from "../shared/directives/ng-var.directive";
import {MainComponent} from "./main/main.component";
import {MatCard} from "@angular/material/card";
import {MatCalendar, MatDatepickerModule} from "@angular/material/datepicker";
import {ThemeToggleComponent} from "../shared/components/theme-toggle.component";
import {SharedModule} from "../shared/shared.module";
import {LanguageToggleComponent} from "../shared/components/language-toggle.component";
import {TranslatePipe, TranslateModule} from "@ngx-translate/core";
import {LoginComponent} from "./login/login.component";
import {NewsComponent} from "./news/news.component";
import { NewsDetailComponent } from './news-detail/news-detail.component';
import { RegisterComponent } from './register/register.component';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {MatForm<PERSON>ield, MatFormFieldModule} from "@angular/material/form-field";
import {
  MatAutocomplete,
  MatAutocompleteModule,
  MatAutocompleteTrigger,
  MatOption
} from "@angular/material/autocomplete";
import {MatInput, MatInputModule} from "@angular/material/input";
import {
  MatCell,
  MatCellDef,
  MatColumnDef,
  MatHeaderCell,
  MatHeaderCellDef,
  MatHeaderRow, MatHeaderRowDef,
  MatRow, MatRowDef, MatTable
} from "@angular/material/table";
import {MatButton} from "@angular/material/button";
import {CoursesComponent} from "./courses/courses.component";
import { CourseDetailComponent } from './course-detail/course-detail.component';
import {RouterLink} from "@angular/router";
import {MatProgressSpinner} from "@angular/material/progress-spinner";
import {MatTab, MatTabGroup} from "@angular/material/tabs";
import { AssignmentsComponent } from './assignments/assignments.component';
import { StudyProgramComponent } from './study-program/study-program.component';
import { CourseGradesComponent } from './course-grades/course-grades.component';
import { CoursePeopleComponent } from './course-people/course-people.component';
import { AssignmentDetailComponent } from './assignment-detail/assignment-detail.component';
import { ProfileComponent } from './profile/profile.component';
import { ScheduleComponent } from './schedule/schedule.component';
import { RatingsComponent } from './ratings/ratings.component';
import { TasksComponent } from './tasks/tasks.component';
import {EditorComponent} from "@tinymce/tinymce-angular";
import { AddTaskComponent } from './add-task/add-task.component';
import { WeekAddDialogComponent } from './teacher/week-add-dialog/week-add-dialog.component';
import {MatSelect} from "@angular/material/select";
import {MatDialogActions, MatDialogContent, MatDialogTitle} from "@angular/material/dialog";
import { CourseWeeksComponent } from './course-weeks/course-weeks.component';
import { SetRatingComponent } from './teacher/set-rating/set-rating.component';
import {MatTooltip, MatTooltipModule} from "@angular/material/tooltip";
import { TeacherAssignmentsComponent } from './teacher/teacher-assignments/teacher-assignments.component';
import { AssignmentGroupAddDialogComponent } from './teacher/assignment-group-add-dialog/assignment-group-add-dialog.component';
import { SetAttendanceComponent } from './teacher/set-attendance/set-attendance.component';
import { AddSubmissionComponent } from './add-submission/add-submission.component';
import { AssignmentUpdateDialogComponent } from './teacher/assignment-update-dialog/assignment-update-dialog.component';
import { AssignmentDeleteDialogComponent } from './teacher/assignment-delete-dialog/assignment-delete-dialog.component';
import { AssignmentGroupUpdateDialogComponent } from './teacher/assignment-group-update-dialog/assignment-group-update-dialog.component';
import { AssignmentGroupDeleteDialogComponent } from './teacher/assignment-group-delete-dialog/assignment-group-delete-dialog.component';
import { WeekUpdateDialogComponent } from './teacher/week-update-dialog/week-update-dialog.component';
import { WeekDeleteDialogComponent } from './teacher/week-delete-dialog/week-delete-dialog.component';
import { ExamScheduleComponent } from './exam-schedule/exam-schedule.component';
import { CalendarComponent } from './calendar/calendar.component';
import { MatNativeDateModule } from '@angular/material/core';
import { GradeSubmissionComponent } from './teacher/grade-submission/grade-submission.component';
import { TeacherManagementPanelComponent } from './teacher/teacher-management-panel/teacher-management-panel.component';
import { DocumentsComponent } from './documents/documents.component';
import { MatSnackBarModule } from '@angular/material/snack-bar';


@NgModule({
  declarations: [
    MainComponent,
    NewsComponent,
    NewsDetailComponent,
    RegisterComponent,
    CoursesComponent,
    CourseDetailComponent,
    AssignmentsComponent,
    StudyProgramComponent,
    CourseGradesComponent,
    CoursePeopleComponent,
    AssignmentDetailComponent,
    ProfileComponent,
    ScheduleComponent,
    RatingsComponent,
    TasksComponent,
    AddTaskComponent,
    WeekAddDialogComponent,
    CourseWeeksComponent,
    SetRatingComponent,
    TeacherAssignmentsComponent,
    AssignmentGroupAddDialogComponent,
    SetAttendanceComponent,
    AddSubmissionComponent,
    AssignmentUpdateDialogComponent,
    AssignmentDeleteDialogComponent,
    AssignmentGroupUpdateDialogComponent,
    AssignmentGroupDeleteDialogComponent,
    WeekUpdateDialogComponent,
    WeekDeleteDialogComponent,
    ExamScheduleComponent,
    CalendarComponent,
    GradeSubmissionComponent,
    TeacherManagementPanelComponent,
    DocumentsComponent,
    NgVarDirective,
  ],
  imports: [
    NgForOf,
    SlicePipe,
    NgIf,
    NgClass,
    FormsModule,
    MatCard,
    MatCalendar,
    ThemeToggleComponent,
    SharedModule,
    LanguageToggleComponent,
    TranslatePipe,
    NgStyle,
    FormsModule,
    MatFormField,
    MatAutocomplete,
    MatOption,
    ReactiveFormsModule,
    MatAutocompleteTrigger,
    AsyncPipe,
    MatInput,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    MatHeaderCell,
    MatColumnDef,
    MatCell,
    MatButton,
    MatHeaderRow,
    MatRow,
    MatCellDef,
    MatHeaderCellDef,
    MatRowDef,
    MatHeaderRowDef,
    MatTable,
    RouterLink,
    MatProgressSpinner,
    MatTabGroup,
    MatTab,
    EditorComponent,
    MatSelect,
    MatDialogContent,
    MatDialogTitle,
    MatDialogActions,
    MatTooltip,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule,
    TranslateModule,
    FormsModule,
    MatSnackBarModule,
  ],
  exports: []
})
export class ComponentsModule {
}
