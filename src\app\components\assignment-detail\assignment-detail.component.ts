import {Component, OnInit} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {AssignmentService} from "../../core/services/assignment.service";
import {CourseService} from "../../core/services/course.service";
import {MatDialog} from "@angular/material/dialog";
import {MatSnackBar} from "@angular/material/snack-bar";
import {AssignmentUpdateDialogComponent} from "../teacher/assignment-update-dialog/assignment-update-dialog.component";
import {AssignmentDeleteDialogComponent} from "../teacher/assignment-delete-dialog/assignment-delete-dialog.component";
import {SubmissionService} from "../../core/services/submission.service";
import {AuthService} from "../../core/services/auth.service";

@Component({
  selector: 'app-assignment-detail',
  templateUrl: './assignment-detail.component.html',
  styleUrl: './assignment-detail.component.css'
})
export class AssignmentDetailComponent implements OnInit {
  breadcrumbs: { label: string, url?: string }[] = [];
  breadcrumbsLoaded: boolean = false;
  assignment: any;
  course: any;
  threadId: number = 0;
  assignmentId: number = 0;
  selectedFiles: string[] = [];
  addTask: boolean = false;
  showActionMenu: boolean = false;
  submissions: any[] = []; // Initialize as empty array
  filteredSubmissions: any[] = []; // Filtered submissions for display
  isLoadingSubmissions: boolean = false;
  currentUser: any;
  hasSubmitted: boolean = false;
  isDeadlinePassed: boolean = false;
  showGradingForm: { [key: number]: boolean } = {}; // Track which submissions have grading forms open
  expandedSubmissions: { [key: number]: boolean } = {}; // Track which submissions are expanded

  // Search and filter properties
  searchTerm: string = '';
  selectedGradeFilter: string = 'all'; // 'all', 'graded', 'ungraded'
  selectedScoreFilter: string = 'all'; // 'all', 'high', 'medium', 'low'
  isFiltering: boolean = false;

  // Student assignment details
  studentAssignmentDetails: any = null;
  isLoadingStudentDetails: boolean = false;
  studentSubmission: any = null;

  // API URL for file downloads
  private readonly downloadBaseUrl = 'http://localhost:8081/storage/download/';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private assignmentService: AssignmentService,
    private courseService: CourseService,
    private submissionService: SubmissionService,
    private authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.assignmentId = Number(this.route.snapshot.paramMap.get('id'));
    this.threadId = Number(this.route.snapshot.paramMap.get('threadId'));

    if (this.assignmentId) {
      this.getAssignmentDetails(this.assignmentId);
      this.getSubmissions(this.assignmentId);

      // If the user is a student, fetch their specific assignment details
      if (this.currentUser && this.currentUser.role === 'student' && this.currentUser.id) {
        this.getStudentAssignmentDetails(this.assignmentId, this.currentUser.id);
      }
    }

    if (this.threadId) {
      this.courseService.getCourseInfoByThread(this.threadId).subscribe({
        next: (course) => {
          this.course = course;
          this.breadcrumbs = [
            {label: 'Главная', url: '/'},
            {label: 'Курс', url: '/courses'},
            {label: this.course.title, url: `/thread/${this.threadId}`},
            {label: 'Задание',}
          ];

          // Set breadcrumbs as loaded after a short delay to show the loading animation
          setTimeout(() => {
            this.breadcrumbsLoaded = true;
          }, 500);
        },
        error: (err) => {
          console.error('Error loading course info:', err);
          // Even on error, we should eventually show the breadcrumbs component
          setTimeout(() => {
            this.breadcrumbsLoaded = true;
          }, 500);
        }
      });
    } else {
      // If no threadId, still show breadcrumbs after a delay
      setTimeout(() => {
        this.breadcrumbsLoaded = true;
      }, 500);
    }
  }

  /**
   * Fetch assignment details specific to a student
   * @param assignmentId The ID of the assignment
   * @param studentId The ID of the student
   */
  getStudentAssignmentDetails(assignmentId: number, studentId: number): void {
    this.isLoadingStudentDetails = true;

    this.assignmentService.getStudentAssignmentDetails(assignmentId, studentId).subscribe({
      next: (data) => {
        this.studentAssignmentDetails = data;

        // Extract submission from the response if it exists
        if (data && data.submission) {
          // Process the submission data
          const submission = {...data.submission};

          // Convert submitted_at to a proper date format if it's not already
          if (submission.submitted_at) {
            if (typeof submission.submitted_at === 'string') {
              // If it's a string, it's likely an ISO date string
              submission.submitted_at = new Date(submission.submitted_at);
            } else if (submission.submitted_at.seconds) {
              // If it has seconds property, it's likely a Firestore timestamp
              submission.submitted_at = new Date(submission.submitted_at.seconds * 1000);
            }
          }

          this.studentSubmission = submission;
          this.hasSubmitted = true;
        } else {
          this.studentSubmission = null;
          this.hasSubmitted = false;
        }

        // Update deadline status if available
        if (data && data.deadline_status) {
          this.isDeadlinePassed = data.deadline_status.is_overdue;
        }

        console.log('Student assignment details:', this.studentAssignmentDetails);
        this.isLoadingStudentDetails = false;
      },
      error: (err) => {
        console.error('Error fetching student assignment details:', err);
        this.studentAssignmentDetails = null;
        this.studentSubmission = null;
        this.isLoadingStudentDetails = false;
      }
    });
  }

  /**
   * Fetch submissions for the assignment
   */
  getSubmissions(assignmentId: number): void {
    this.isLoadingSubmissions = true;

    this.submissionService.getSubmissions(assignmentId).subscribe({
      next: (data) => {
        // Handle the new API response format which includes assignment_id, count, and submissions array
        if (data && data.submissions) {
          this.submissions = data.submissions || [];
        } else {
          // Fallback for backward compatibility
          this.submissions = Array.isArray(data) ? data : [];
        }

        console.log('Submissions:', this.submissions);

        // Check if the current user has submitted
        if (this.currentUser && this.currentUser.id && this.submissions && this.submissions.length > 0) {
          this.hasSubmitted = this.submissions.some(
            submission => submission.user_id === this.currentUser.id
          );
        } else {
          this.hasSubmitted = false;
        }

        // Initialize filtered submissions with all submissions
        this.filteredSubmissions = [...this.submissions];
        this.isLoadingSubmissions = false;
      },
      error: (err) => {
        console.error('Error fetching submissions:', err);
        this.submissions = []; // Initialize as empty array on error
        this.filteredSubmissions = [];
        this.hasSubmitted = false;
        this.isLoadingSubmissions = false;
      }
    });
  }

  /**
   * Apply search and filters to submissions
   */
  applyFilters(): void {
    // Only apply filtering for teachers and admins
    if (this.currentUser?.role !== 'teacher' && this.currentUser?.role !== 'admin') {
      this.filteredSubmissions = [...this.submissions];
      this.isFiltering = false;
      return;
    }

    this.isFiltering = this.searchTerm.trim() !== '' ||
                      this.selectedGradeFilter !== 'all' ||
                      this.selectedScoreFilter !== 'all';

    // Start with all submissions
    this.filteredSubmissions = [...this.submissions];

    // Apply search term filter
    if (this.searchTerm.trim() !== '') {
      const searchLower = this.searchTerm.toLowerCase().trim();
      this.filteredSubmissions = this.filteredSubmissions.filter(submission => {
        // Search in user name, surname, email
        const userMatch = submission.user && (
          (submission.user.name && submission.user.name.toLowerCase().includes(searchLower)) ||
          (submission.user.surname && submission.user.surname.toLowerCase().includes(searchLower)) ||
          (submission.user.email && submission.user.email.toLowerCase().includes(searchLower))
        );

        // Search in comment
        const commentMatch = submission.comment &&
                           submission.comment.toLowerCase().includes(searchLower);

        return userMatch || commentMatch ||
               (submission.user_id && submission.user_id.toString().includes(searchLower));
      });
    }

    // Apply grade filter
    if (this.selectedGradeFilter !== 'all') {
      if (this.selectedGradeFilter === 'graded') {
        this.filteredSubmissions = this.filteredSubmissions.filter(
          submission => submission.score !== undefined && submission.score !== null
        );
      } else if (this.selectedGradeFilter === 'ungraded') {
        this.filteredSubmissions = this.filteredSubmissions.filter(
          submission => submission.score === undefined || submission.score === null
        );
      }
    }

    // Apply score filter
    if (this.selectedScoreFilter !== 'all' && this.assignment && this.assignment.max_points) {
      const maxPoints = this.assignment.max_points;

      if (this.selectedScoreFilter === 'high') {
        // High scores: >= 80% of max points
        this.filteredSubmissions = this.filteredSubmissions.filter(
          submission => submission.score !== undefined &&
                      submission.score !== null &&
                      submission.score >= (maxPoints * 0.8)
        );
      } else if (this.selectedScoreFilter === 'medium') {
        // Medium scores: 50-80% of max points
        this.filteredSubmissions = this.filteredSubmissions.filter(
          submission => submission.score !== undefined &&
                      submission.score !== null &&
                      submission.score >= (maxPoints * 0.5) &&
                      submission.score < (maxPoints * 0.8)
        );
      } else if (this.selectedScoreFilter === 'low') {
        // Low scores: < 50% of max points
        this.filteredSubmissions = this.filteredSubmissions.filter(
          submission => submission.score !== undefined &&
                      submission.score !== null &&
                      submission.score < (maxPoints * 0.5)
        );
      }
    }
  }

  /**
   * Handle search input
   */
  onSearchInput(): void {
    this.applyFilters();
  }

  /**
   * Clear search term
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.applyFilters();
  }

  /**
   * Change grade filter
   */
  onGradeFilterChange(filter: string): void {
    this.selectedGradeFilter = filter;
    this.applyFilters();
  }

  /**
   * Change score filter
   */
  onScoreFilterChange(filter: string): void {
    this.selectedScoreFilter = filter;
    this.applyFilters();
  }

  /**
   * Reset all filters
   */
  resetFilters(): void {
    // Only apply for teachers and admins
    if (this.currentUser?.role !== 'teacher' && this.currentUser?.role !== 'admin') {
      return;
    }

    this.searchTerm = '';
    this.selectedGradeFilter = 'all';
    this.selectedScoreFilter = 'all';
    this.isFiltering = false;
    this.filteredSubmissions = [...this.submissions];
  }

  toggleTask(): void {
    this.addTask = !this.addTask;
  }

  /**
   * Handle submission complete event from add-submission component
   */
  onSubmissionComplete(): void {
    // Hide the submission form
    this.addTask = false;

    // Refresh submissions
    this.getSubmissions(this.assignmentId);

    // If the user is a student, refresh their specific assignment details
    if (this.currentUser && this.currentUser.role === 'student' && this.currentUser.id) {
      this.getStudentAssignmentDetails(this.assignmentId, this.currentUser.id);
    }
  }

  toggleActionMenu(): void {
    this.showActionMenu = !this.showActionMenu;
  }

  closeActionMenu(): void {
    this.showActionMenu = false;
  }

  openEditDialog(): void {
    // Check if user is a teacher or admin
    if (this.currentUser?.role !== 'teacher' && this.currentUser?.role !== 'admin') {
      this.snackBar.open('Доступ запрещен: Только преподаватели могут редактировать задания', 'Закрыть', {
        duration: 5000,
        panelClass: ['snackbar-error'],
      });
      return;
    }

    this.closeActionMenu();

    const dialogRef = this.dialog.open(AssignmentUpdateDialogComponent, {
      width: '800px',
      data: {
        assignment: this.assignment,
        threadId: this.threadId
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.updateAssignment(result);
      }
    });
  }

  openDeleteDialog(): void {
    // Check if user is a teacher or admin
    if (this.currentUser?.role !== 'teacher' && this.currentUser?.role !== 'admin') {
      this.snackBar.open('Доступ запрещен: Только преподаватели могут удалять задания', 'Закрыть', {
        duration: 5000,
        panelClass: ['snackbar-error'],
      });
      return;
    }

    this.closeActionMenu();

    const dialogRef = this.dialog.open(AssignmentDeleteDialogComponent, {
      width: '500px',
      data: {
        assignment: this.assignment
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteAssignment();
      }
    });
  }

  updateAssignment(formData: any): void {
    // Check if user is a teacher or admin
    if (this.currentUser?.role !== 'teacher' && this.currentUser?.role !== 'admin') {
      this.snackBar.open('Доступ запрещен: Только преподаватели могут обновлять задания', 'Закрыть', {
        duration: 5000,
        panelClass: ['snackbar-error'],
      });
      return;
    }

    this.assignmentService.updateAssignment(
      this.assignmentId,
      formData.week_id,
      formData.title,
      formData.description,
      formData.due_date,
      formData.max_points,
      formData.type,
      formData.assignment_group_id
    ).subscribe({
      next: () => {
        this.snackBar.open('Задание успешно обновлено', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-success']
        });
        this.getAssignmentDetails(this.assignmentId);
      },
      error: (err) => {
        console.error('Ошибка при обновлении задания:', err);
        this.snackBar.open('Ошибка при обновлении задания', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  deleteAssignment(): void {
    // Check if user is a teacher or admin
    if (this.currentUser?.role !== 'teacher' && this.currentUser?.role !== 'admin') {
      this.snackBar.open('Доступ запрещен: Только преподаватели могут удалять задания', 'Закрыть', {
        duration: 5000,
        panelClass: ['snackbar-error'],
      });
      return;
    }

    this.assignmentService.deleteAssignment(this.assignmentId).subscribe({
      next: () => {
        this.snackBar.open('Задание успешно удалено', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-success']
        });
        // Navigate back to the course page
        this.router.navigate([`/thread/${this.threadId}`]);
      },
      error: (err) => {
        console.error('Ошибка при удалении задания:', err);
        this.snackBar.open('Ошибка при удалении задания', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  getAssignmentDetails(id: number): void {
    this.assignmentService.getAssignmentDetails(id).subscribe({
      next: data => {
        // Handle the new API response format
        if (data && data.assignment) {
          // The new API response format includes assignment and attachments
          this.assignment = {
            ...data.assignment,
            attachments: data.attachments || []
          };
        } else {
          // Fallback for backward compatibility
          this.assignment = data;
        }

        console.log('Детали задания:', this.assignment);

        // Check if deadline has passed
        if (this.assignment && this.assignment.due_date) {
          this.checkDeadline();
        }
      },
      error: err => {
        console.error('Ошибка при загрузке задания', err);
      }
    });
  }

  /**
   * Check if the assignment deadline has passed
   */
  checkDeadline(): void {
    if (!this.assignment || !this.assignment.due_date) {
      this.isDeadlinePassed = false;
      return;
    }

    let dueDate: Date;

    // Handle different date formats
    if (this.assignment.due_date.seconds) {
      // Firestore timestamp format
      dueDate = new Date(this.assignment.due_date.seconds * 1000);
    } else if (typeof this.assignment.due_date === 'string') {
      // ISO string format
      dueDate = new Date(this.assignment.due_date);
    } else {
      // Unknown format
      this.isDeadlinePassed = false;
      return;
    }

    const now = new Date();
    this.isDeadlinePassed = now > dueDate;

    console.log('Deadline check:', {
      dueDate: dueDate,
      now: now,
      isDeadlinePassed: this.isDeadlinePassed
    });
  }

  onFileSelected(event: Event, index: number): void {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (file) {
      this.selectedFiles[index - 1] = file.name;
    }
  }

  /**
   * Toggle the grading form for a specific submission
   * @param submissionId The ID of the submission
   */
  toggleGradingForm(submissionId: number): void {
    // Initialize if not exists
    if (this.showGradingForm[submissionId] === undefined) {
      this.showGradingForm[submissionId] = false;
    }

    // Toggle the value
    this.showGradingForm[submissionId] = !this.showGradingForm[submissionId];

    // Make sure the submission stays expanded when the grading form is open
    if (this.showGradingForm[submissionId]) {
      this.expandedSubmissions[submissionId] = true;
    }
  }

  /**
   * Toggle the expanded state of a submission
   * @param submissionId The ID of the submission
   * @param event Optional mouse event to prevent propagation
   */
  toggleSubmissionExpanded(submissionId: number, event?: MouseEvent): void {
    // Prevent event propagation if provided
    if (event) {
      event.stopPropagation();
    }

    // Don't toggle if the grading form is open for this submission
    if (this.showGradingForm[submissionId]) {
      return;
    }

    // Initialize if not exists
    if (this.expandedSubmissions[submissionId] === undefined) {
      this.expandedSubmissions[submissionId] = false;
    }

    // Toggle the value
    this.expandedSubmissions[submissionId] = !this.expandedSubmissions[submissionId];
  }

  /**
   * Strip HTML tags from a string
   * @param html HTML string to strip tags from
   * @returns Plain text without HTML tags
   */
  stripHtmlTags(html: string): string {
    if (!html) return '';
    // Create a temporary div element
    const tempDiv = document.createElement('div');
    // Set the HTML content
    tempDiv.innerHTML = html;
    // Return the text content
    return tempDiv.textContent || tempDiv.innerText || '';
  }

  /**
   * Calculate the average score for all graded submissions
   * @returns The average score or null if no graded submissions
   */
  calculateAverageScore(): number | null {
    if (!this.submissions || this.submissions.length === 0) {
      return null;
    }

    // Filter submissions that have a score
    const gradedSubmissions = this.submissions.filter(
      submission => submission.score !== undefined && submission.score !== null
    );

    if (gradedSubmissions.length === 0) {
      return null;
    }

    // Calculate the sum of all scores
    const sum = gradedSubmissions.reduce(
      (total, submission) => total + submission.score, 0
    );

    // Return the average
    return sum / gradedSubmissions.length;
  }

  /**
   * Handle grading complete event from grade-submission component
   * @param response The response from the grading API
   */
  onGradingComplete(response: any): void {
    // Store the expanded state of submissions before refreshing
    const expandedState = {...this.expandedSubmissions};

    // Close all grading forms
    Object.keys(this.showGradingForm).forEach(key => {
      this.showGradingForm[Number(key)] = false;
    });

    // Refresh submissions to show updated grades
    this.getSubmissions(this.assignmentId);

    // If the graded submission belongs to the current student, refresh their details
    if (response && response.user_id === this.currentUser?.id) {
      this.getStudentAssignmentDetails(this.assignmentId, this.currentUser.id);
    }

    // After submissions are refreshed, restore the expanded state
    setTimeout(() => {
      this.expandedSubmissions = {...expandedState};
      // Make sure the graded submission stays expanded
      if (response && response.id) {
        this.expandedSubmissions[response.id] = true;
      }
    }, 500); // Wait for submissions to be loaded
  }

  /**
   * Get the download URL for a file
   * @param fileUrl The file URL from the API response
   * @returns The full download URL
   */
  getDownloadUrl(fileUrl: string): string {
    // If the URL already starts with http, return it as is
    if (fileUrl.startsWith('http')) {
      return fileUrl;
    }

    // Otherwise, prepend the download base URL
    return this.downloadBaseUrl + fileUrl;
  }
}
