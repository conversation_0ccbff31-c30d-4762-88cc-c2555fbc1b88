@use '@angular/material' as mat;

// Include the common styles for Angular Material
@include mat.core();

// Define the palettes for your theme using the Material Design palettes
$primary: mat.define-palette(mat.$blue-palette);
$accent: mat.define-palette(mat.$blue-palette, A200, A100, A400);
$warn: mat.define-palette(mat.$red-palette);

// Create the theme object
$theme: mat.define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
    warn: $warn,
  )
));

// Include theme styles for core and each component
@include mat.all-component-themes($theme);

// Define a dark theme
$dark-theme: mat.define-dark-theme((
  color: (
    primary: $primary,
    accent: $accent,
    warn: $warn,
  )
));

// Apply the dark theme only when the `.dark` class is present on the body
.dark {
  @include mat.all-component-colors($dark-theme);
}

// Apply SF Pro font to Angular Material components
.mat-typography {
  font-family: 'SF Pro', 'SF Pro Cyrillic Fallback', system-ui, Avenir, Helvetica, Arial, sans-serif !important;
}

// Apply font styles to specific Angular Material components
.mat-mdc-button,
.mat-mdc-raised-button,
.mat-mdc-outlined-button,
.mat-mdc-unelevated-button,
.mat-mdc-menu-item,
.mat-mdc-form-field,
.mat-mdc-select,
.mat-mdc-option,
.mat-mdc-tab,
.mat-mdc-card-title,
.mat-mdc-card-subtitle,
.mat-mdc-card-content,
.mat-mdc-dialog-title,
.mat-mdc-dialog-content,
.mat-mdc-dialog-actions {
  font-family: 'SF Pro', 'SF Pro Cyrillic Fallback', system-ui, Avenir, Helvetica, Arial, sans-serif !important;
}
