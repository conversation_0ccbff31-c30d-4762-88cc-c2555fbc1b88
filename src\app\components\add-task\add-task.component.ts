import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {EditorComponent} from "@tinymce/tinymce-angular";
import {environments} from "../../environments/environments";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {MatSnackBar} from "@angular/material/snack-bar";
import {AssignmentService} from "../../core/services/assignment.service";
import {WeekService} from "../../core/services/week.service";
import {data} from "autoprefixer";
import {StorageService} from "../../core/services/storage.service";
import {Router} from "@angular/router";

@Component({
  selector: 'app-add-task',
  templateUrl: './add-task.component.html',
  styleUrl: './add-task.component.css'
})
export class AddTaskComponent implements OnInit, OnChanges {
  @Input() threadID: any;


  constructor(private fb: FormBuilder,
              private snackBar: MatSnackBar,
              private assignmentService: AssignmentService,
              private weekService: WeekService,
              private storageService: StorageService,
              private router: Router) {
  }


  breadcrumbs: { label: string, url?: string }[] = [
    {label: 'Главная', url: '/'},
    {label: 'Добавить Урок',},
  ]

  editor_key = environments.TINY_MC_KEY
  init: EditorComponent['init'] = {
    plugins: 'lists link image table code  wordcount'
  };

  assignmentGroups: any;
  weeks: any;
  assignmentForm!: FormGroup;
  minDate: string = '';
  selectedFile: File | null = null;

  ngOnInit() {
    // Check if user is a teacher or admin
    const user = this.storageService.getUser();
    if (!user || (user.role !== 'teacher' && user.role !== 'admin')) {
      this.snackBar.open('Доступ запрещен: Только преподаватели могут добавлять задания', 'Закрыть', {
        duration: 5000,
        panelClass: ['snackbar-error'],
      });
      this.router.navigate(['/']);
      return;
    }

    // Set minimum date to today
    const today = new Date();
    today.setMinutes(today.getMinutes() - today.getTimezoneOffset());
    this.minDate = today.toISOString().slice(0, 16);

    this.assignmentForm = this.fb.group({
      week_id: [null, Validators.required],
      title: ['', [Validators.required, Validators.minLength(5)]],
      description: [''],
      due_date: ['', Validators.required],
      max_points: [null, [Validators.required, Validators.min(0), Validators.max(100)]],
      type: ['task', Validators.required], // Default to 'task'
      assignment_group_id: [null, Validators.required],
    });

    if (this.threadID) {
      this.getListOfAssignments(this.threadID);
      this.getAllWeeksByThread(this.threadID);
    }

    // Listen for changes to the type field
    this.assignmentForm.get('type')?.valueChanges.subscribe(type => {
      this.handleTypeChange(type);
    });
  }

  /**
   * Handle changes to the assignment type
   */
  handleTypeChange(type: string): void {
    const maxPointsControl = this.assignmentForm.get('max_points');
    const dueDateControl = this.assignmentForm.get('due_date');

    if (type === 'info') {
      // For info type, set max_points to null and disable
      maxPointsControl?.setValue(null);
      maxPointsControl?.disable();

      // Clear due_date and disable the field
      dueDateControl?.setValue(null);
      dueDateControl?.disable();
    } else {
      // For task type, enable controls
      maxPointsControl?.enable();
      dueDateControl?.enable();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
  }

  getListOfAssignments(threadID: number) {
    this.assignmentService.getListAssignmentGroupForThread(threadID).subscribe({
      next: data => {
        this.assignmentGroups = data;
        console.log('assignments groups', this.assignmentGroups)
      },
      error: err => {
        console.log(err);
      }
    })
  }

  getAllWeeksByThread(threadID: number) {
    this.weekService.getAllWeeksByThread(threadID).subscribe({
      next: data => {
        this.weeks = data;
        console.log('weeks ', this.weeks)
      },
      error: err => {
        console.log(err);
      }
    })
  }

  /**
   * Handle file selection
   */
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.selectedFile = input.files[0];
    }
  }

  /**
   * Remove selected file
   */
  removeFile(): void {
    this.selectedFile = null;
  }

  createAssignment() {
    // Check if user is a teacher or admin
    const user = this.storageService.getUser();
    if (!user || (user.role !== 'teacher' && user.role !== 'admin')) {
      this.snackBar.open('Доступ запрещен: Только преподаватели могут создавать задания', 'Закрыть', {
        duration: 5000,
        panelClass: ['snackbar-error'],
      });
      return;
    }

    // Get both enabled and disabled controls
    const formValues: Record<string, any> = {
      ...this.assignmentForm.value,
      ...Object.keys(this.assignmentForm.controls)
        .filter(key => this.assignmentForm.controls[key].disabled)
        .reduce((acc: Record<string, any>, key: string) => {
          acc[key] = this.assignmentForm.controls[key].value;
          return acc;
        }, {})
    };

    console.log('Form values:', formValues);

    if (this.assignmentForm.invalid) {
      this.assignmentForm.markAllAsTouched();

      this.snackBar.open('Форма недействительна проверьте еще раз', 'Закрыть', {
        duration: 3000,
        panelClass: ['snackbar-error'],
      });
      return;
    }

    const {week_id, title, description, due_date, max_points, type, assignment_group_id} = formValues;

    // Create FormData for multipart/form-data submission
    const formData = new FormData();
    formData.append('title', title);
    formData.append('description', description);
    formData.append('type', type);

    // Only include due_date for task type
    if (type === 'task' && due_date) {
      const isoDate = new Date(due_date).toISOString();
      formData.append('due_date', isoDate);
    }

    if (assignment_group_id) {
      formData.append('assignment_group_id', assignment_group_id.toString());
    }

    // Only include max_points for task type
    if (type === 'task' && max_points !== null) {
      formData.append('max_points', max_points.toString());
    }

    // Append file if selected
    if (this.selectedFile) {
      formData.append('file', this.selectedFile);
    }

    this.assignmentService
      .createAssignmentWithFile(+week_id, formData)
      .subscribe({
        next: (res) => {
          console.log('Успешно создано задание:', res);

          this.snackBar.open('Задание успешно создано', 'Закрыть', {
            duration: 6000,
            panelClass: ['snackbar-success'],
          });

          this.assignmentForm.reset();
          // Reset to default values
          this.assignmentForm.patchValue({
            type: 'task'
          });
          // Reset file
          this.selectedFile = null;
        },
        error: (err) => {
          console.error('Ошибка создания:', err);

          this.snackBar.open('Ошибка при создании задания', 'Закрыть', {
            duration: 3000,
            panelClass: ['snackbar-error'],
          });
        },
      });
  }


}
