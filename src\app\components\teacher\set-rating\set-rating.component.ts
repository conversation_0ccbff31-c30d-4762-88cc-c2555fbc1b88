import {Component} from '@angular/core';

@Component({
  selector: 'app-set-rating',
  templateUrl: './set-rating.component.html',
  styleUrl: './set-rating.component.css'
})
export class SetRatingComponent {
  students = [
    { id: 1, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 2, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 3, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 4, name: 'Гүльназ', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 5, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 6, name: '<PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 7, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 8, name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 9, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 10, name: '<PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 11, name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', surname: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 12, name: '<PERSON><PERSON><PERSON><PERSON>ла', surname: '<PERSON>лиева' },
    { id: 13, name: '<PERSON>имур', surname: '<PERSON>хметов' },
    { id: 14, name: '<PERSON>инара', surname: 'Смагулова' },
    { id: 15, name: '<PERSON>рман', surname: '<PERSON>олатов' },
    { id: 16, name: '<PERSON>еруерт', surname: 'Омарова' },
    { id: 17, name: 'Расул', surname: 'Тулегенов' },
    { id: 18, name: 'Айгерим', surname: 'Жангельдина' },
    { id: 19, name: 'Санжар', surname: 'Мусин' },
    { id: 20, name: 'Асель', surname: 'Ким' },
  ]

  assignments = ['Math HW1', 'Physics Lab10','Physics Lab4','Physics Lab3' , 'Physics Lab4','Physics Lab3',
    'Physics Lab4','Physics Lab3','Physics Lab4','Physics Lab3']; // Названия заданий


}
