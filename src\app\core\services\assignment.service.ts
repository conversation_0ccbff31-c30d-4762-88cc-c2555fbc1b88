import {Injectable} from '@angular/core';
import {environments} from "../../environments/environments";
import {HttpClient} from "@angular/common/http";
import {Router} from "@angular/router";
import {StorageService} from "./storage.service";
import {Observable, catchError, of, throwError} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class AssignmentService {

  private apiUrl = environments.API

  constructor(
    private http: HttpClient,
    private storage: StorageService
  ) {
  }

  private checkTeacherRole(): boolean {
    const user = this.storage.getUser();
    return user && (user.role === 'teacher' || user.role === 'admin');
  }

  getListAssignmentGroupForThread(thread_id: number) {
    return this.http.get<any>(`${this.apiUrl}/assignments/groups/thread/${thread_id}`);
  }

  getAssignmentDetails(assignment_id: number) {
    return this.http.get<any>(`${this.apiUrl}/assignments/assignments/${assignment_id}`);
  }

  /**
   * Get assignment details with student-specific information
   * @param assignment_id The ID of the assignment
   * @param student_id The ID of the student
   * @returns Observable with assignment details, submission, and grade information
   */
  getStudentAssignmentDetails(assignment_id: number, student_id: number) {
    return this.http.get<any>(`${this.apiUrl}/assignments/assignments/${assignment_id}/student/${student_id}`);
  }


  createAssignmentGroup(thread_id: number, name: string, group_type: string, weight: number) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can create assignment groups'));
    }

    return this.http.post<{ message: string } | { error: string }>(
      `${this.apiUrl}/assignments/groups`,
      {thread_id, name, group_type, weight}
    );
  }

  createAssignment(week_id: number, title: string, description: string,
                   due_date: string | null, max_points: number | null, type: string, assignment_group_id: number) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can create assignments'));
    }

    // Create payload based on assignment type
    const payload: any = { title, description, type, assignment_group_id };

    // Only include due_date and max_points for task type
    if (type === 'task') {
      if (due_date) payload.due_date = due_date;
      if (max_points !== null) payload.max_points = max_points;
    }

    return this.http.post<{ message: string } | { error: string }>(
      `${this.apiUrl}/assignments/weeks/${week_id}/assignments`,
      payload
    );
  }

  /**
   * Create a new assignment with file upload support
   * @param week_id The ID of the week
   * @param formData FormData containing assignment details and optional file
   * @returns Observable with the created assignment
   */
  createAssignmentWithFile(week_id: number, formData: FormData) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can create assignments'));
    }

    return this.http.post<{ message: string } | { error: string }>(
      `${this.apiUrl}/assignments/weeks/${week_id}/assignments`,
      formData
    );
  }

  deleteAssignment(assignment_id: number) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can delete assignments'));
    }

    return this.http.delete(`${this.apiUrl}/assignments/assignments/${assignment_id}`);
  }

  updateAssignment(assignment_id: number, week_id: number, title: string, description: string,
                   due_date: string | null, max_points: number | null, type: string, assignment_group_id: number) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can update assignments'));
    }

    // Create payload based on assignment type
    const payload: any = { week_id, title, description, type, assignment_group_id };

    // Only include due_date and max_points for task type
    if (type === 'task') {
      if (due_date) payload.due_date = due_date;
      if (max_points !== null) payload.max_points = max_points;
    }

    return this.http.put(`${this.apiUrl}/assignments/assignments/${assignment_id}`, payload);
  }

  /**
   * Update an assignment with file upload support
   * @param assignment_id The ID of the assignment to update
   * @param formData FormData containing assignment details and optional file
   * @returns Observable with the updated assignment
   */
  updateAssignmentWithFile(assignment_id: number, formData: FormData) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can update assignments'));
    }

    return this.http.put<{ message: string } | { error: string }>(
      `${this.apiUrl}/assignments/assignments/${assignment_id}`,
      formData
    );
  }

  updateAssignmentGroup(group_id: number, thread_id: number, name: string, group_type: string, weight: number) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can update assignment groups'));
    }

    return this.http.put(`${this.apiUrl}/assignments/groups/${group_id}`, {
      thread_id, name, group_type, weight
    });
  }

  deleteAssignmentGroup(group_id: number) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can delete assignment groups'));
    }

    return this.http.delete(`${this.apiUrl}/assignments/groups/${group_id}`);
  }

  /**
   * Get pending assignments for a student
   * @param student_id The ID of the student
   * @returns Observable with pending assignments
   */
  getPendingAssignments(student_id: number): Observable<any> {
    return this.http.get<any>(
      `${this.apiUrl}/students/${student_id}/pending-assignments`
    ).pipe(
      catchError(error => {
        console.error('Error fetching pending assignments:', error);
        return of({ assignments: [], count: 0, student_id });
      })
    );
  }

}
