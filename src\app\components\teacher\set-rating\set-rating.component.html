<div class="relative overflow-x-auto  mb-10 border sm:rounded-lg ">
  <table class="w-min text-xs text-left text-gray-500 relative">
    <thead class="text-sm  text-blue-400 capitalize bg-gray-100 sticky z-10 top-0">
    <tr  >
      <th class="px-2 py-1 sticky bg-gray-100 border-r left-0 z-30">№</th>
      <th class="px-6 py-1 sticky bg-gray-100 border-r left-[2rem] z-20">Студент</th>

      <th
        *ngFor="let assignment of assignments"
        class="px-6 py-1 text-nowrap"
        [matTooltip]="assignment"
      >
        {{ assignment.slice(0, 8) }}{{ assignment.length > 8 ? '..' : '' }}
      </th>


    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let student of students; let i = index"
        class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">

      <!-- Номер -->
      <td class="px-2 py-1  text-black bg-gray-100 border-r sticky left-0 z-20">
        {{ i + 1 }}
      </td>

      <!-- Имя студента -->
      <td class="px-6 py-1 text-nowrap font-medium text-gray-900 bg-gray-100 border-r sticky left-[2rem] z-10">
        {{ student.name }} {{ student.surname }}
      </td>

      <!-- Оценки -->
      <td *ngFor="let assignment of assignments" class="px-6 py-1">
        <input type="text" class="w-10 border outline-red-300
         bg-transparent px-2 py-1 rounded appearance-none">
      </td>
    </tr>
    </tbody>
  </table>

</div>


