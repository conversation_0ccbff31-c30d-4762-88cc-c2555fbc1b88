import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private themeKey = 'theme';
  private isDarkMode = false;
  private themeSubject = new BehaviorSubject<'light' | 'dark'>('light');

  constructor() {
    if (this.isBrowser()) {
      this.loadTheme();
    }
  }

  toggleTheme(): void {
    if (!this.isBrowser()) return;

    // Toggle theme state
    this.isDarkMode = !this.isDarkMode;
    const newTheme = this.isDarkMode ? 'dark' : 'light';
    localStorage.setItem(this.themeKey, newTheme);
    this.themeSubject.next(newTheme);
    this.applyTheme();
  }

  loadTheme(): void {
    if (!this.isBrowser()) return;

    const savedTheme = localStorage.getItem(this.themeKey);
    this.isDarkMode = savedTheme === 'dark';
    this.themeSubject.next(this.isDarkMode ? 'dark' : 'light');
    this.applyTheme();
  }

  applyTheme(): void {
    if (!this.isBrowser()) return;

    if (this.isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }

  isDark(): boolean {
    return this.isDarkMode;
  }

  getTheme(): 'light' | 'dark' {
    return this.isDarkMode ? 'dark' : 'light';
  }

  getThemeObservable(): Observable<'light' | 'dark'> {
    return this.themeSubject.asObservable();
  }

  private isBrowser(): boolean {
    return typeof window !== 'undefined' &&
      typeof document !== 'undefined' &&
      typeof localStorage !== 'undefined';
  }
}
