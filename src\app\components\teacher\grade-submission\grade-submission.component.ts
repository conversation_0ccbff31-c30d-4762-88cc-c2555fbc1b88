import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { SubmissionService } from '../../../core/services/submission.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-grade-submission',
  templateUrl: './grade-submission.component.html',
  styleUrl: './grade-submission.component.css'
})
export class GradeSubmissionComponent implements OnInit {
  @Input() submission: any;
  @Input() maxPoints: number = 100;
  @Output() gradingComplete = new EventEmitter<any>();

  gradeForm: FormGroup;
  isSubmitting: boolean = false;

  constructor(
    private fb: FormBuilder,
    private submissionService: SubmissionService,
    private snackBar: MatSnackBar,
    private translate: TranslateService
  ) {
    this.gradeForm = this.fb.group({
      score: [null, [Validators.required, Validators.min(0), Validators.max(100)]],
      feedback: ['', [Validators.required, Validators.minLength(3)]]
    });
  }

  ngOnInit(): void {
    // If the submission already has a score and feedback, pre-fill the form
    if (this.submission && this.submission.score !== undefined) {
      this.gradeForm.patchValue({
        score: this.submission.score,
        feedback: this.submission.feedback || ''
      });
    }

    // Update the max score validator based on the maxPoints input
    if (this.maxPoints) {
      this.gradeForm.get('score')?.setValidators([
        Validators.required,
        Validators.min(0),
        Validators.max(this.maxPoints)
      ]);
      this.gradeForm.get('score')?.updateValueAndValidity();
    }
  }

  submitGrade(): void {
    if (this.gradeForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.gradeForm.controls).forEach(key => {
        const control = this.gradeForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const { score, feedback } = this.gradeForm.value;

    this.submissionService.gradeSubmission(
      this.submission.id,
      score,
      feedback
    ).subscribe({
      next: (response) => {
        this.translate.get('ASSIGNMENTS.graded_successfully').subscribe((message: string) => {
          this.snackBar.open(message, 'Закрыть', {
            duration: 3000,
            panelClass: ['snackbar-success']
          });
        });
        this.isSubmitting = false;
        this.gradingComplete.emit(response);
      },
      error: (error) => {
        console.error('Error grading submission:', error);
        this.snackBar.open(
          error.error?.error || 'Ошибка при сохранении оценки',
          'Закрыть',
          {
            duration: 5000,
            panelClass: ['snackbar-error']
          }
        );
        this.isSubmitting = false;
      }
    });
  }

  // Helper method to get form control error messages
  getErrorMessage(controlName: string): string {
    const control = this.gradeForm.get(controlName);

    if (!control || !control.errors || !control.touched) {
      return '';
    }

    if (control.errors['required']) {
      return 'Это поле обязательно';
    }

    if (controlName === 'score') {
      if (control.errors['min']) {
        return 'Оценка не может быть меньше 0';
      }
      if (control.errors['max']) {
        return `Оценка не может быть больше ${this.maxPoints}`;
      }
    }

    if (controlName === 'feedback' && control.errors['minlength']) {
      return 'Отзыв должен содержать не менее 3 символов';
    }

    return 'Неверное значение';
  }
}
