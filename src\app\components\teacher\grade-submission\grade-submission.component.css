/* Custom styles for the grading component */
:host {
  display: block;
  margin-top: 1rem;
}

.form-input {
  @apply w-full px-3 py-2 bg-input border border-input rounded-lg focus:outline-none focus:border-input-focus;
}

/* Spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
