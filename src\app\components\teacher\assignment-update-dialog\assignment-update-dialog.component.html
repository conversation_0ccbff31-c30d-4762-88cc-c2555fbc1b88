<div class="max-w-screen-xl max-h-screen">
  <div class="font-medium text-base p-4 border-b">
    Редактировать задание
  </div>
  <div class="text-sm max-w-4xl p-4">
    <form [formGroup]="assignmentForm" (ngSubmit)="submit()">
      <div class="mb-5 grid grid-cols-1 gap-4">
        <!-- Заголовок -->
        <div>
          <label class="block text-sm font-medium text-gray-500 mb-1">
            Название задания:
          </label>
          <input
            type="text"
            formControlName="title"
            class="block w-full rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600"
          />
          <div *ngIf="assignmentForm.get('title')?.invalid && assignmentForm.get('title')?.touched"
               class="text-xs text-red-500 mt-1">
            Название должно содержать минимум 5 символов
          </div>
        </div>

        <!-- Группа заданий -->
        <div>
          <label class="block text-sm font-medium text-gray-500 mb-1">
            Группа заданий:
          </label>
          <div class="relative">
            <select formControlName="assignment_group_id"
                    class="block w-full appearance-none rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600">
              <option value="" disabled>Выберите группу</option>
              <option *ngFor="let group of assignmentGroups" [value]="group.id">
                {{ group.name }}
              </option>
            </select>
          </div>
        </div>

        <!-- Неделя -->
        <div>
          <label class="block text-sm font-medium text-gray-500 mb-1">
            Неделя:
          </label>
          <div class="relative">
            <select formControlName="week_id"
                    class="block w-full appearance-none rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600">
              <option value="" disabled>Выберите неделю</option>
              <option *ngFor="let week of weeks" [value]="week.id">
                Неделя {{ week.week_number }}: {{ week.title }}
              </option>
            </select>
          </div>
        </div>

        <!-- Тип задания -->
        <div>
          <label class="block text-sm font-medium text-gray-500 mb-1">
            Тип задания:
          </label>
          <div class="relative">
            <select formControlName="type"
                    class="block w-full appearance-none rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600">
              <option value="info">Лекция</option>
              <option value="task">Задание</option>
            </select>
          </div>
        </div>

        <!-- Срок сдачи -->
        <div>
          <label class="block text-sm font-medium text-gray-500 mb-1">
            Срок сдачи:
          </label>
          <input
            type="datetime-local"
            formControlName="due_date"
            [min]="minDate"
            class="block w-full rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600"
          />
          <div *ngIf="assignmentForm.get('type')?.value === 'info'" class="text-xs text-gray-500 mt-1">
            Для лекций дедлайн не требуется.
          </div>
        </div>

        <!-- Максимальные баллы -->
        <div>
          <label class="block text-sm font-medium text-gray-500 mb-1">
            Максимальные баллы:
          </label>
          <input
            type="number"
            formControlName="max_points"
            min="0"
            max="100"
            class="block w-full rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600"
          />
          <div *ngIf="assignmentForm.get('type')?.value === 'info'" class="text-xs text-gray-500 mt-1">
            Для лекций баллы не требуются.
          </div>
        </div>

        <!-- Описание -->
        <div>
          <label class="block text-sm font-medium text-gray-500 mb-1">
            Описание:
          </label>
          <editor
            formControlName="description"
            [init]="init"
            apiKey="{{editor_key}}"
          ></editor>
        </div>
      </div>

      <div class="flex justify-end space-x-2 mt-4">
        <button
          type="button"
          (click)="cancel()"
          class="px-4 py-2 text-sm rounded-lg border hover:bg-gray-100"
        >
          Отмена
        </button>
        <button
          type="submit"
          [disabled]="assignmentForm.invalid"
          class="px-4 py-2 text-sm rounded-lg bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50"
        >
          Сохранить
        </button>
      </div>
    </form>
  </div>
</div>


