import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../core/services/auth.service';
import { TranslateService } from '@ngx-translate/core';
import { ThreadService } from '../../core/services/thread.service';

interface CourseRating {
  id: number;
  title: string;
  code: string;
  score: number;
  current: number;
  letterGrade: string;
  gpa: number;
  credits: number;
  assignments: AssignmentRating[];
}

interface AssignmentRating {
  id: number;
  title: string;
  score: number;
  maxScore: number;
  weight: number;
  date: string;
}

interface SemesterRating {
  id: number;
  name: string;
  gpa: number;
  credits: number;
}

interface RegisteredCourse {
  id: number;
  thread: any;
  course: any;
}

@Component({
  selector: 'app-ratings',
  templateUrl: './ratings.component.html',
  styleUrl: './ratings.component.css'
})
export class RatingsComponent implements OnInit {
  // User info
  user: any;
  isTeacher: boolean = false;

  // UI state
  activeTab: 'current' | 'history' | 'details' = 'current';
  selectedCourse: CourseRating | null = null;
  selectedSemester: string = 'current';
  isLoading: boolean = false;

  // Student registered courses
  registeredCourses: any[] = [];
  isLoadingRegisteredCourses: boolean = false;

  // Breadcrumbs
  breadcrumbs: { label: string, url?: string }[] = [
    { label: 'Главная', url: '/' },
    { label: 'Оценки' },
  ];

  // Data
  courses: CourseRating[] = [
    {
      id: 1,
      title: 'Marketing Digitale Avanzato',
      code: 'MKT301',
      score: 90,
      current: 0,
      letterGrade: 'A-',
      gpa: 3.7,
      credits: 3,
      assignments: [
        { id: 1, title: 'Midterm Exam', score: 85, maxScore: 100, weight: 30, date: '2023-10-15' },
        { id: 2, title: 'Final Project', score: 92, maxScore: 100, weight: 40, date: '2023-12-10' },
        { id: 3, title: 'Case Study', score: 88, maxScore: 100, weight: 20, date: '2023-11-05' },
        { id: 4, title: 'Participation', score: 95, maxScore: 100, weight: 10, date: '2023-12-15' }
      ]
    },
    {
      id: 2,
      title: 'UI/UX Design',
      code: 'DES202',
      score: 75,
      current: 0,
      letterGrade: 'C+',
      gpa: 2.3,
      credits: 4,
      assignments: [
        { id: 5, title: 'Design Portfolio', score: 78, maxScore: 100, weight: 50, date: '2023-12-01' },
        { id: 6, title: 'Usability Testing', score: 72, maxScore: 100, weight: 30, date: '2023-11-15' },
        { id: 7, title: 'Wireframing Exercise', score: 75, maxScore: 100, weight: 20, date: '2023-10-20' }
      ]
    },
    {
      id: 3,
      title: 'Business Analytics',
      code: 'BUS305',
      score: 83,
      current: 0,
      letterGrade: 'B',
      gpa: 3.0,
      credits: 3,
      assignments: [
        { id: 8, title: 'Data Analysis Project', score: 85, maxScore: 100, weight: 40, date: '2023-11-25' },
        { id: 9, title: 'Midterm Exam', score: 80, maxScore: 100, weight: 30, date: '2023-10-10' },
        { id: 10, title: 'Case Presentations', score: 84, maxScore: 100, weight: 30, date: '2023-12-05' }
      ]
    },
    {
      id: 4,
      title: 'Artificial Intelligence',
      code: 'CS401',
      score: 48,
      current: 0,
      letterGrade: 'F',
      gpa: 0.0,
      credits: 4,
      assignments: [
        { id: 11, title: 'Algorithm Implementation', score: 45, maxScore: 100, weight: 35, date: '2023-11-10' },
        { id: 12, title: 'Research Paper', score: 50, maxScore: 100, weight: 25, date: '2023-10-25' },
        { id: 13, title: 'Final Exam', score: 48, maxScore: 100, weight: 40, date: '2023-12-15' }
      ]
    },
    {
      id: 5,
      title: 'Django Framework',
      code: 'CS310',
      score: 88,
      current: 0,
      letterGrade: 'B+',
      gpa: 3.3,
      credits: 3,
      assignments: [
        { id: 14, title: 'Web Application Project', score: 90, maxScore: 100, weight: 50, date: '2023-12-10' },
        { id: 15, title: 'Code Review', score: 85, maxScore: 100, weight: 25, date: '2023-11-15' },
        { id: 16, title: 'API Design', score: 88, maxScore: 100, weight: 25, date: '2023-10-30' }
      ]
    },
    {
      id: 6,
      title: 'Project Management',
      code: 'MGT320',
      score: 92,
      current: 0,
      letterGrade: 'A-',
      gpa: 3.7,
      credits: 3,
      assignments: [
        { id: 17, title: 'Project Plan', score: 94, maxScore: 100, weight: 30, date: '2023-10-20' },
        { id: 18, title: 'Team Leadership', score: 90, maxScore: 100, weight: 30, date: '2023-11-25' },
        { id: 19, title: 'Final Presentation', score: 92, maxScore: 100, weight: 40, date: '2023-12-15' }
      ]
    }
  ];

  semesters: SemesterRating[] = [
    { id: 1, name: 'Fall 2023', gpa: 3.2, credits: 20 },
    { id: 2, name: 'Spring 2023', gpa: 3.5, credits: 18 },
    { id: 3, name: 'Fall 2022', gpa: 3.1, credits: 19 },
    { id: 4, name: 'Spring 2022', gpa: 3.7, credits: 17 }
  ];

  // Computed properties
  get currentGPA(): number {
    if (this.courses.length === 0) return 0;

    const totalCredits = this.courses.reduce((sum, course) => sum + course.credits, 0);
    const weightedGPA = this.courses.reduce((sum, course) => sum + (course.gpa * course.credits), 0);

    return totalCredits > 0 ? parseFloat((weightedGPA / totalCredits).toFixed(2)) : 0;
  }

  get totalCredits(): number {
    return this.courses.reduce((sum, course) => sum + course.credits, 0);
  }

  constructor(
    private authService: AuthService,
    private translateService: TranslateService,
    private threadService: ThreadService
  ) {}

  ngOnInit() {
    // Get user info
    this.user = this.authService.getCurrentUser();
    this.isTeacher = this.user?.role === 'teacher';

    // Redirect teachers to home page if they somehow access this page
    if (this.isTeacher) {
      // Navigate to home page
      window.location.href = '/';
      return;
    }

    // Fetch registered courses for students
    if (this.user && this.user.id) {
      this.fetchRegisteredCourses();
    }

    // Animate progress circles
    this.courses.forEach(course => {
      this.animateProgress(course);
    });
  }

  /**
   * Fetch the student's registered courses
   */
  fetchRegisteredCourses() {
    if (!this.user || !this.user.id) return;

    this.isLoadingRegisteredCourses = true;

    this.threadService.getThreadsWithScheduleForUser(this.user.id).subscribe({
      next: (data: any) => {
        if (data && data.threads) {
          this.registeredCourses = data.threads;
        }
        this.isLoadingRegisteredCourses = false;
      },
      error: (err) => {
        console.error('Error fetching registered courses:', err);

        // Fallback to the old endpoint
        this.threadService.getListOfThreadsForUser(this.user.id).subscribe({
          next: (fallbackData: any) => {
            if (fallbackData && fallbackData.threads) {
              this.registeredCourses = fallbackData.threads;
            }
            this.isLoadingRegisteredCourses = false;
          },
          error: (fallbackErr) => {
            console.error('Error fetching registered courses (fallback):', fallbackErr);
            this.isLoadingRegisteredCourses = false;
          }
        });
      }
    });
  }

  // Tab navigation
  setActiveTab(tab: 'current' | 'history' | 'details') {
    this.activeTab = tab;
  }

  // Select course for detailed view
  selectCourse(course: CourseRating) {
    this.selectedCourse = course;
    this.setActiveTab('details');
  }

  // Select semester for history view
  changeSemester(semesterId: string) {
    this.selectedSemester = semesterId;
    // In a real app, you would fetch data for the selected semester here
  }

  // Animation for progress circles
  animateProgress(course: CourseRating) {
    const stepTime = 7;
    const interval = setInterval(() => {
      if (course.current < course.score) {
        course.current += 1;
      } else {
        clearInterval(interval);
      }
    }, stepTime);
  }

  // Color coding for grades
  getColor(val: number): string {
    if (val < 50) return '#ef4444'; // red
    if (val < 80) return '#facc15'; // yellow
    return '#22c55e';              // green
  }

  // Get letter grade from percentage
  getLetterGrade(score: number): string {
    if (score >= 93) return 'A';
    if (score >= 90) return 'A-';
    if (score >= 87) return 'B+';
    if (score >= 83) return 'B';
    if (score >= 80) return 'B-';
    if (score >= 77) return 'C+';
    if (score >= 73) return 'C';
    if (score >= 70) return 'C-';
    if (score >= 67) return 'D+';
    if (score >= 65) return 'D';
    return 'F';
  }

  // Get GPA value from percentage
  getGPAValue(score: number): number {
    if (score >= 93) return 4.0;
    if (score >= 90) return 3.7;
    if (score >= 87) return 3.3;
    if (score >= 83) return 3.0;
    if (score >= 80) return 2.7;
    if (score >= 77) return 2.3;
    if (score >= 73) return 2.0;
    if (score >= 70) return 1.7;
    if (score >= 67) return 1.3;
    if (score >= 65) return 1.0;
    return 0.0;
  }

  // Back to course list from details view
  backToCourses() {
    this.selectedCourse = null;
    this.setActiveTab('current');
  }
}
