import {Component, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {MOCK_DATA} from "../../mock-data";
import {ViewModeService} from "../../../core/services/view-mode.service";
import {ThreadService} from "../../../core/services/thread.service";
import {timestampToDate} from "../../utils/date.utils";
import {AuthService} from "../../../core/services/auth.service";
import {Subscription} from 'rxjs';

@Component({
  selector: 'app-courses-list',
  templateUrl: './courses-list.component.html',
  styleUrl: './courses-list.component.css'
})
export class CoursesListComponent implements OnInit, OnDestroy {
  constructor(private viewModeService: ViewModeService,
              private threadService: ThreadService,
              private authService: AuthService) {
  }

  user: any;
  viewMode: 'card' | 'columns' = 'card';
  courses: any = [];
  user_threads: any[] = [];
  filteredThreads: any[] = [];
  searchQuery: string = '';
  noResults: boolean = false;
  loading: boolean = true;

  private subscriptions: Subscription[] = [];

  @Input() gridClasses: string = 'grid 2xl:grid-cols-4 xl:grid-cols-3 grid-cols-1 gap-4 gap-x-2';

  ngOnInit() {
    this.user = this.authService.getCurrentUser();

    if (this.user) {
      if (this.user.role === 'teacher') {
        this.getTeacherThreads();
      } else {
        this.getUserThreads(this.user.id);
      }
    }

    // Subscribe to view mode changes
    this.subscriptions.push(
      this.viewModeService.viewMode$.subscribe(mode => {
        this.viewMode = mode;
      })
    );

    // Subscribe to search query changes
    this.subscriptions.push(
      this.viewModeService.searchQuery$.subscribe(query => {
        this.searchQuery = query;
        this.filterThreads();
      })
    );
  }

  ngOnDestroy() {
    // Clean up subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  getUserThreads(id: number) {
    this.loading = true;
    this.threadService.getListOfThreadsForUser(id).subscribe({
      next: (data: any) => {
        this.user_threads = data.threads; // ВАЖНО: достаем threads
        this.filteredThreads = [...this.user_threads]; // Initialize filtered threads
        this.filterThreads(); // Apply any existing search filter
        console.log(this.user_threads);
        this.loading = false;
      },
      error: err => {
        console.log(err);
        this.loading = false;
      }
    });
  }

  getTeacherThreads() {
    this.loading = true;
    this.threadService.getTeacherThreads().subscribe({
      next: (data: any) => {
        // Transform teacher threads to match the structure expected by the template
        if (data && data.threads) {
          this.user_threads = data.threads.map((thread: any) => {
            return {
              thread: thread,
              course: thread.course,
              teacher: data.teacher
            };
          });
          this.filteredThreads = [...this.user_threads];
          this.filterThreads();
        }
        console.log('Teacher threads:', this.user_threads);
        this.loading = false;
      },
      error: err => {
        console.log('Error fetching teacher threads:', err);
        this.loading = false;
      }
    });
  }

  filterThreads() {
    if (!this.searchQuery || this.searchQuery.trim() === '') {
      this.filteredThreads = [...this.user_threads];
      this.noResults = this.filteredThreads.length === 0;
      return;
    }

    const query = this.searchQuery.toLowerCase().trim();

    this.filteredThreads = this.user_threads.filter(thread => {
      // Handle different data structures based on user role
      let threadTitle = '';
      let courseTitle = '';
      let teacherName = '';

      // Check if thread has the expected structure
      if (thread.thread && typeof thread.thread === 'object') {
        threadTitle = thread.thread.title || '';
      } else if (thread.title) {
        // For teacher view where thread might be directly in the object
        threadTitle = thread.title || '';
      }

      // Check for course information
      if (thread.course && thread.course.title) {
        courseTitle = thread.course.title;
      }

      // Check for teacher information
      if (thread.teacher && thread.teacher.name) {
        teacherName = thread.teacher.name;
      }

      // Perform the search
      const threadTitleMatch = threadTitle.toLowerCase().includes(query);
      const courseTitleMatch = courseTitle.toLowerCase().includes(query);
      const teacherNameMatch = teacherName.toLowerCase().includes(query);

      return threadTitleMatch || courseTitleMatch || teacherNameMatch;
    });

    this.noResults = this.filteredThreads.length === 0;
  }

  mockCourses = MOCK_DATA.mockCourses;
}
