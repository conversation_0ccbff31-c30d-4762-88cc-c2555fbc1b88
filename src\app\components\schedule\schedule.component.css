/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Schedule item hover effect */
.grid.border-b:hover {
  background-color: rgba(243, 244, 246, 0.5);
}

/* Dark mode adjustments */
:host-context(.dark) .grid.border-b:hover {
  background-color: rgba(55, 65, 81, 0.3);
}