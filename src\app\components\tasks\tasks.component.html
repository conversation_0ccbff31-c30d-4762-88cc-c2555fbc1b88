<app-breadcrumbs *ngIf="showDescription" [items]="breadcrumbs"/>

<div class="max-w-screen-xl mb-5">
  <div *ngIf="showDescription">
    <div class="font-medium text-blue-700 dark:text-blue-400">
      {{ 'ASSIGNMENTS.assignments' | translate }}
    </div>
    <div class="text-sm font-gotham mb-5 flex gap-2 text-gray-700 dark:text-gray-300">
      {{ 'ASSIGNMENTS.description_text' | translate }}
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-10">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
  </div>

  <!-- Error Message -->
  <div *ngIf="error && !isLoading" class="bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded mb-4">
    <p>{{ error | translate }}</p>
  </div>

  <!-- No Assignments Message -->
  <div *ngIf="!isLoading && !error && !groupedAssignments.today.length && !groupedAssignments.tomorrow.length && groupedLaterDates.length === 0"
       class="bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 px-4 py-8 rounded mb-4 text-center">
    <p>{{ 'ASSIGNMENTS.no_pending_assignments' | translate }}</p>
  </div>

  <div class="max-w-4xl" *ngIf="!isLoading && !error">
    <div class="">
      <!-- Блок: Сегодня -->
      <!-- Сегодня -->
      <div *ngIf="groupedAssignments.today.length" class="mb-8">
        <div class="flex gap-2 items-center mb-3">
          <div class="text-base font-medium text-red-700 dark:text-red-400">{{ 'ASSIGNMENTS.today' | translate }}</div>
          <img src="/assets/icons/fire2.svg" class="size-4" alt="">
        </div>

        <div class="relative ml-14 pl-4 border-l-2 border-gray-300 dark:border-gray-700 space-y-4">
          <div *ngFor="let a of groupedAssignments.today"
               class="mb-3 relative group transition-all duration-500 bg-red-50 dark:bg-red-900/20 rounded-md shadow-sm">
            <!-- Время -->
            <div
              class="absolute -left-[70px] p-2 text-sm text-red-500 dark:text-red-400 group-hover:text-blue-500 transition-all duration-500">
              {{ a.due_date | date: 'HH:mm' }}
            </div>
            <!-- Контент -->
            <div class="p-3 group-hover:text-blue-500 transition-all duration-500">
              <div class="flex justify-between items-start">
                <div>
                  <div class="text-sm font-medium">{{ a.title }}</div>
                  <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">{{ a.thread.course.title }} - {{ a.thread.title }}</div>
                </div>
                <div class="text-xs text-red-600 dark:text-red-400 font-medium">
                  {{ a.deadline_status.status_text }}
                </div>
              </div>
              <div class="flex justify-between items-center mt-2">
                <button (click)="navigateToAssignment(a)"
                   class="group cursor-pointer inline-flex items-center gap-1 text-xs px-3 py-1.5 border rounded-md shadow-sm text-gray-700 dark:text-gray-300 dark:border-gray-700 transition hover:bg-gray-50 dark:hover:bg-gray-700">
                  {{ 'ASSIGNMENTS.open' | translate }}
                  <span class="transition-transform group-hover:translate-x-1">→</span>
                </button>
                <div *ngIf="a.max_points" class="text-xs font-medium">
                  {{ 'ASSIGNMENTS.max_points' | translate }}: {{ a.max_points }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Завтра -->
      <div *ngIf="groupedAssignments.tomorrow.length" class="mb-8">
        <div class="text-base font-medium text-yellow-600 dark:text-yellow-400 mb-3">{{ 'ASSIGNMENTS.tomorrow' | translate }}</div>
        <div class="relative ml-14 pl-4 border-l-2 border-gray-300 dark:border-gray-700 space-y-4">
          <div *ngFor="let a of groupedAssignments.tomorrow"
               class="mb-3 relative group transition-all duration-500 bg-yellow-50 dark:bg-yellow-900/20 rounded-md shadow-sm">
            <div
              class="absolute -left-[70px] p-2 text-sm text-yellow-600 dark:text-yellow-400 group-hover:text-blue-500 transition-all duration-500">
              {{ a.due_date | date: 'HH:mm' }}
            </div>
            <div class="p-3 group-hover:text-blue-500 transition-all duration-500">
              <div class="flex justify-between items-start">
                <div>
                  <div class="text-sm font-medium">{{ a.title }}</div>
                  <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">{{ a.thread.course.title }} - {{ a.thread.title }}</div>
                </div>
                <div class="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
                  {{ a.deadline_status.status_text }}
                </div>
              </div>
              <div class="flex justify-between items-center mt-2">
                <button (click)="navigateToAssignment(a)"
                   class="group cursor-pointer inline-flex items-center gap-1 text-xs px-3 py-1.5 border rounded-md shadow-sm text-gray-700 dark:text-gray-300 dark:border-gray-700 transition hover:bg-gray-50 dark:hover:bg-gray-700">
                  {{ 'ASSIGNMENTS.open' | translate }}
                  <span class="transition-transform group-hover:translate-x-1">→</span>
                </button>
                <div *ngIf="a.max_points" class="text-xs font-medium">
                  {{ 'ASSIGNMENTS.max_points' | translate }}: {{ a.max_points }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Позже -->
      <div *ngFor="let date of groupedLaterDates" class="mb-8">
        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">{{ date }}</div>
        <div class="relative ml-14 pl-4 border-l-2 border-gray-300 dark:border-gray-700 space-y-4">
          <div *ngFor="let a of groupedAssignments.later[date]"
               class="mb-3 relative group transition-all duration-500 bg-gray-50 dark:bg-gray-800 rounded-md shadow-sm">
            <div
              class="absolute -left-[70px] p-2 text-sm text-gray-500 dark:text-gray-400 group-hover:text-blue-500 transition-all duration-500">
              {{ a.due_date | date: 'HH:mm' }}
            </div>
            <div class="p-3 group-hover:text-blue-500 transition-all duration-500">
              <div class="flex justify-between items-start">
                <div>
                  <div class="text-sm font-medium">{{ a.title }}</div>
                  <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">{{ a.thread.course.title }} - {{ a.thread.title }}</div>
                </div>
                <div class="text-xs text-gray-600 dark:text-gray-400 font-medium">
                  {{ a.deadline_status.status_text }}
                </div>
              </div>
              <div class="flex justify-between items-center mt-2">
                <button (click)="navigateToAssignment(a)"
                   class="group cursor-pointer inline-flex items-center gap-1 text-xs px-3 py-1.5 border rounded-md shadow-sm text-gray-700 dark:text-gray-300 dark:border-gray-700 transition hover:bg-gray-50 dark:hover:bg-gray-700">
                  {{ 'ASSIGNMENTS.open' | translate }}
                  <span class="transition-transform group-hover:translate-x-1">→</span>
                </button>
                <div *ngIf="a.max_points" class="text-xs font-medium">
                  {{ 'ASSIGNMENTS.max_points' | translate }}: {{ a.max_points }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
