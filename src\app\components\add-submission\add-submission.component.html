<div class="xl:w-8/12 w-full">
  <div class="mb-4">
    <div class="text-sm font-medium text-gray-700 mb-2">Ответ на задание:</div>
    <editor
      [apiKey]="editor_key"
      [init]="init"
      [(ngModel)]="comment"
      (onEditorChange)="onEditorChange($event)">
    </editor>
  </div>

  <div class="mb-4">
    <div class="text-sm font-medium text-gray-700 mb-2">Прикрепить файлы:</div>
    <div class="flex flex-col space-y-2">
      <!-- File input -->
      <label class="flex items-center justify-center px-4 py-2 border border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        <span class="text-sm text-gray-500">Выберите файл</span>
        <input type="file" class="hidden" (change)="onFileSelected($event)">
      </label>
      <div class="text-xs text-gray-500 mt-1">
        Рекомендуется загружать по одному файлу для лучшей совместимости с сервером.
      </div>

      <!-- Selected files list -->
      <div *ngIf="selectedFiles.length > 0" class="mt-2">
        <div *ngFor="let file of selectedFiles; let i = index" class="flex items-center justify-between py-2 px-3 bg-gray-50 rounded mb-1">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span class="text-xs text-gray-700 truncate max-w-xs">{{ file.name }}</span>
          </div>
          <button (click)="removeFile(i)" class="text-red-500 hover:text-red-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="mt-4">
    <button
      (click)="submitAssignment()"
      [disabled]="isSubmitting || (selectedFiles.length === 0 && !comment)"
      class="text-sm bg-blue-600 py-2 px-4 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center">
      <span *ngIf="isSubmitting" class="mr-2">
        <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </span>
      {{ isSubmitting ? 'Отправка...' : 'Отправить' }}
    </button>
  </div>
</div>

