import {Component, Input, OnInit} from '@angular/core';
import {Assignments} from "../../shared/mock-data";
import {AssignmentService} from "../../core/services/assignment.service";
import {AuthService} from "../../core/services/auth.service";
import {MatSnackBar} from "@angular/material/snack-bar";
import {Router} from "@angular/router";
import {BreadcrumbsComponent} from "../../shared/components/breadcrumbs/breadcrumbs.component";

interface PendingAssignment {
  id: number;
  assignment_group_id: number;
  created_at: string;
  deadline_status: {
    days_remaining: number;
    is_overdue: boolean;
    status_text: string;
  };
  description: string;
  due_date: string;
  max_points: number;
  thread: {
    course: {
      id: number;
      title: string;
    };
    id: number;
    title: string;
  };
  title: string;
  type: string;
  updated_at: string;
  week_id: number;
}

@Component({
  selector: 'app-tasks',
  templateUrl: './tasks.component.html',
  styleUrl: './tasks.component.css'
})
export class TasksComponent implements OnInit {

  @Input() showDescription: boolean = true;
  @Input() showButton: boolean = true;

  isLoading: boolean = false;
  error: string | null = null;
  currentUser: any;

  groupedLaterDates: string[] = [];
  groupedAssignments: {
    today: PendingAssignment[],
    tomorrow: PendingAssignment[],
    later: { [key: string]: PendingAssignment[] }
  } = {
    today: [],
    tomorrow: [],
    later: {}
  };

  get groupedDates(): string[] {
    return Object.keys(this.groupedAssignments.later);
  }

  breadcrumbs: { label: string, url?: string }[] = [
    {label: 'BREADCRUMB.HOME', url: '/'},
    {label: 'BREADCRUMB.TASKS',}
  ]

  constructor(
    private assignmentService: AssignmentService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {
    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit() {
    this.loadPendingAssignments();
  }

  loadPendingAssignments() {
    if (!this.currentUser || !this.currentUser.id) {
      this.error = 'ERROR.USER_NOT_AUTHENTICATED';
      this.showError(this.error);
      return;
    }

    // Check if user is a student
    if (this.currentUser.role !== 'student') {
      this.error = 'ERROR.STUDENTS_ONLY';
      this.showError(this.error);
      return;
    }

    this.isLoading = true;
    this.assignmentService.getPendingAssignments(this.currentUser.id).subscribe({
      next: (data) => {
        this.isLoading = false;
        if (data && data.assignments) {
          this.processAssignments(data.assignments);
        } else {
          this.groupedAssignments = { today: [], tomorrow: [], later: {} };
          this.groupedLaterDates = [];
        }
      },
      error: (err) => {
        this.isLoading = false;
        this.error = 'ERROR.LOADING_ASSIGNMENTS';
        this.showError(this.error);
      }
    });
  }

  processAssignments(assignments: PendingAssignment[]) {
    // Filter out assignments with type "info"
    const filteredAssignments = assignments.filter(a => a.type !== 'info');

    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);

    this.groupedAssignments = {
      today: filteredAssignments.filter(a => this.isSameDay(new Date(a.due_date), today)),
      tomorrow: filteredAssignments.filter(a => this.isSameDay(new Date(a.due_date), tomorrow)),
      later: this.groupByDate(filteredAssignments.filter(a => {
        const due = new Date(a.due_date);
        return !this.isSameDay(due, today) && !this.isSameDay(due, tomorrow);
      }))
    };

    this.groupedLaterDates = Object.keys(this.groupedAssignments.later);
  }

  isSameDay(date1: Date, date2: Date): boolean {
    return date1.toDateString() === date2.toDateString();
  }

  groupByDate(assignments: PendingAssignment[]): { [key: string]: PendingAssignment[] } {
    return assignments.reduce((acc, curr) => {
      const key = new Date(curr.due_date).toLocaleDateString();
      if (!acc[key]) acc[key] = [];
      acc[key].push(curr);
      return acc;
    }, {} as { [key: string]: PendingAssignment[] });
  }

  showError(message: string) {
    this.snackBar.open(message, 'COMMON.close', {
      duration: 5000,
      panelClass: ['snackbar-error']
    });
  }

  navigateToAssignment(assignment: PendingAssignment) {
    // Navigate to the assignment detail page using the router
    this.router.navigate(['/thread', assignment.thread.id, 'assignments', assignment.id]);
  }
}
