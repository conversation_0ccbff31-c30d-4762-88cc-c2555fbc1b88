import {Component, OnInit} from '@angular/core';
import {DropdownItem} from "../../shared/components/dropdown/dropdown.component";
import {ViewModeService} from "../../core/services/view-mode.service";
import {Subject, debounceTime, distinctUntilChanged} from 'rxjs';

@Component({
  selector: 'app-courses',
  templateUrl: './courses.component.html',
  styleUrl: './courses.component.css'
})
export class CoursesComponent implements OnInit {

  breadcrumbs: { label: string, url?: string }[] = [];
  searchQuery: string = '';
  searchSubject = new Subject<string>();
  isSearching: boolean = false;

  buttonLabel: string = 'View';
  menuItems: DropdownItem[] = [
    { label: 'Card', type: 'button', icon: 'assets/icons/card.svg' },
    { label: 'Columns', type: 'button', icon: 'assets/icons/table.svg' }
  ];
  currentView: 'card' | 'columns' = 'card';

  constructor(private viewModeService: ViewModeService) {
    // Initialize search with debounce
    this.searchSubject.pipe(
      debounceTime(300), // Wait for 300ms after the last event
      distinctUntilChanged() // Only emit if value is different from previous
    ).subscribe(query => {
      this.searchQuery = query;
      this.performSearch();
    });
  }

  ngOnInit() {
    this.viewModeService.viewMode$.subscribe(mode => {
      this.currentView = mode;
    });

    this.breadcrumbs = [
      { label: 'BREADCRUMB.HOME', url: '/' },
      { label: 'BREADCRUMB.COURSES',   },
    ];
  }

  changeView(item: DropdownItem) {
    const mode = item.label === 'Card' ? 'card' : 'columns';
    this.viewModeService.setViewMode(mode);
  }

  onSearchInput(event: Event) {
    const query = (event.target as HTMLInputElement).value;
    this.isSearching = query.length > 0;
    this.searchSubject.next(query);
  }

  clearSearch() {
    this.searchQuery = '';
    this.isSearching = false;
    this.searchSubject.next('');
  }

  performSearch() {
    // This will be used by the courses-list component to filter courses
    this.viewModeService.setSearchQuery(this.searchQuery);
  }
}
