import {Component, OnInit} from '@angular/core';
import {MatDialog} from "@angular/material/dialog";
import {MOCK_DATA} from "../../shared/mock-data";
import {NewsService} from "../../core/services/news.service";

@Component({
  selector: 'app-news',
  templateUrl: './news.component.html',
  styleUrl: './news.component.css'
})
export class NewsComponent implements OnInit {

  breadcrumbs: { label: string, url?: string }[] = [
    { label: 'Главная', url: '/' },
    { label: 'Новости',  },
  ];


  latestNews = MOCK_DATA.latestNews;
  deleteButton: boolean = false


  constructor(private dialog: MatDialog,
              private newsService: NewsService) {
  }

  ngOnInit() {
    this.getAllPost()
  }

  getAllPost() {
    // this.newsService.getAllPost().subscribe({
    //   next: (response) => {
    //     this.news = response.map((item: any) => {
    //       return {
    //         ...item, // Копируем все свойства объекта
    //         image_url: item.image_url.replace('http://minio:9000', 'http://localhost:9000') // Заменяем базовый URL
    //       };
    //     });
    //
    //     // console.log(this.news);
    //     this.lastNews = this.news[response.length - 1]
    //   }, error: err => {
    //     // console.log(err)
    //   }
    // })
  }

}
