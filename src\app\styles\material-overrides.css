/* Material Design Component Overrides */

/* Apply SF Pro font to Angular Material components */
.mat-typography {
  font-family: 'SF Pro', 'SF Pro Cyrillic Fallback', system-ui, Avenir, Helvetica, Arial, sans-serif !important;
}

/* Apply font styles to specific Angular Material components */
.mat-mdc-button,
.mat-mdc-raised-button,
.mat-mdc-outlined-button,
.mat-mdc-unelevated-button,
.mat-mdc-menu-item,
.mat-mdc-form-field,
.mat-mdc-select,
.mat-mdc-option,
.mat-mdc-tab,
.mat-mdc-card-title,
.mat-mdc-card-subtitle,
.mat-mdc-card-content,
.mat-mdc-dialog-title,
.mat-mdc-dialog-content,
.mat-mdc-dialog-actions {
  font-family: 'SF Pro', 'SF Pro Cyrillic Fallback', system-ui, Avenir, Helvetica, Arial, sans-serif !important;
}

/* Fix for Cyrillic text in Material components */
[lang="ru"] .mat-mdc-menu-item,
[lang="ru"] .mat-mdc-form-field,
[lang="ru"] .mat-mdc-select,
[lang="ru"] .mat-mdc-option,
[lang="ru"] .mat-mdc-card-title,
[lang="ru"] .mat-mdc-card-subtitle,
[lang="ru"] .mat-mdc-card-content,
[lang="ru"] .mat-mdc-dialog-title,
[lang="ru"] .mat-mdc-dialog-content,
[lang="kk"] .mat-mdc-menu-item,
[lang="kk"] .mat-mdc-form-field,
[lang="kk"] .mat-mdc-select,
[lang="kk"] .mat-mdc-option,
[lang="kk"] .mat-mdc-card-title,
[lang="kk"] .mat-mdc-card-subtitle,
[lang="kk"] .mat-mdc-card-content,
[lang="kk"] .mat-mdc-dialog-title,
[lang="kk"] .mat-mdc-dialog-content {
  font-family: Arial, Helvetica, sans-serif !important;
  letter-spacing: normal;
}

/* Fix for Material form fields */
.mat-mdc-form-field {
  font-size: 14px;
}

/* Fix for Material buttons */
.mat-mdc-button,
.mat-mdc-raised-button,
.mat-mdc-outlined-button,
.mat-mdc-unelevated-button {
  font-size: 14px;
  font-weight: 500;
}

/* Fix for Material menu items */
.mat-mdc-menu-item {
  font-size: 14px;
}

/* Fix for Material tabs */
.mat-mdc-tab {
  font-size: 14px;
  font-weight: 500;
}

/* Fix for Material card titles */
.mat-mdc-card-title {
  font-size: 20px;
  font-weight: 500;
}

/* Fix for Material card subtitles */
.mat-mdc-card-subtitle {
  font-size: 14px;
}

/* Fix for Material dialog titles */
.mat-mdc-dialog-title {
  font-size: 20px;
  font-weight: 500;
}

/* Fix for Material dialog content */
.mat-mdc-dialog-content {
  font-size: 14px;
}
