import {Component, Input, OnInit} from '@angular/core';
import {ThreadService} from "../../core/services/thread.service";
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'app-course-people',
  templateUrl: './course-people.component.html',
  styleUrl: './course-people.component.css'
})
export class CoursePeopleComponent implements OnInit {

  @Input() threadID: any;

  members: any[] = [];
  filteredMembers: any[] = [];
  searchQuery: string = '';
  selectedRole: string = 'all';
  isLoading: boolean = false;

  constructor(private threadService: ThreadService) {}

  ngOnInit() {
    if (this.threadID) {
      this.getAllMembers(this.threadID);
    }
  }

  getAllMembers(thread_id: number) {
    this.isLoading = true;
    this.threadService.getThreadMembers(thread_id).subscribe({
      next: (data: any) => {
        this.members = data.members;
        this.filteredMembers = [...this.members];
        console.log("members", this.members);
        this.isLoading = false;
      },
      error: err => {
        console.log(err);
        this.isLoading = false;
      }
    });
  }

  /**
   * Filter members based on search query and selected role
   */
  filterMembers() {
    // Start with all members
    let filtered = [...this.members];

    // Apply search filter if query exists
    if (this.searchQuery && this.searchQuery.trim() !== '') {
      const query = this.searchQuery.toLowerCase().trim();
      filtered = filtered.filter(member =>
        member.name?.toLowerCase().includes(query) ||
        member.email?.toLowerCase().includes(query) ||
        member.id?.toString().includes(query) ||
        member.role?.toLowerCase().includes(query)
      );
    }

    // Apply role filter if not 'all'
    if (this.selectedRole !== 'all') {
      filtered = filtered.filter(member =>
        member.role?.toLowerCase() === this.selectedRole.toLowerCase()
      );
    }

    this.filteredMembers = filtered;
  }

  /**
   * Filter members by role
   */
  filterByRole(role: string) {
    this.selectedRole = role;
    this.filterMembers();
  }

  /**
   * Get initials from name
   */
  getInitials(name: string): string {
    if (!name) return '?';

    const parts = name.split(' ');
    if (parts.length === 1) {
      return name.charAt(0).toUpperCase();
    }

    return (parts[0].charAt(0) + parts[1].charAt(0)).toUpperCase();
  }
}
