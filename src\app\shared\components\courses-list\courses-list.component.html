<!-- Card View -->
<div *ngIf="viewMode === 'card' && !loading" [class]="gridClasses">
  <ng-container *ngFor="let thread of filteredThreads; let index">
    <div class="border dark:border-gray-600 dark:bg-[#0d1117] w-full cursor-pointer p-2 bg-white rounded-xl flex items-center min-h-[160px]
                      transition-transform duration-500 hover:shadow-md">
      <!-- Handle different thread structures based on user role -->
      <a [routerLink]="['/thread', user.role === 'teacher' ? thread.thread.id : thread.thread.id]" class="px-2 w-full">
        <div>
          <div>
            <img src="/assets/icons/learn.svg" class="size-8 bg-gray-300 p-1 rounded-full" alt="">
          </div>
        </div>
        <div class="mt-2">
          <a href="" class="text-sm font-medium cursor-pointer hover:text-blue-400 transition-colors">
            {{ thread.course?.title }}
          </a>
          <div class="text-xs font-medium">
            <ng-container  >
              {{ 'COURSES.teacher' | translate }}: {{ thread.teacher?.name }}
            </ng-container>
          </div>
          <div class="text-xs text-gray-700 dark:text-gray-400 text-wrap">
            {{ 'COURSES.thread_number' | translate }}:
            {{ user.role === 'teacher' ? thread.thread.title : thread.thread.title }}
          </div>
          <div class="text-xs mt-3 flex gap-2">
            <a href=""
               class="shadow w-max rounded-full p-1 px-2 flex items-center gap-1
                      hover:bg-blue-400 hover:text-white group transition-colors duration-100 dark:border dark:border-gray-600 ">
              <img src="/assets/icons/file.svg"
                   class="size-3.5 invert dark:invert-0 group-hover:invert group-hover:brightness-0 group-hover:contrast-200"
                   alt="">
            </a>
            <a href=""
               class="shadow w-max rounded-full p-1 px-2 flex items-center gap-1
                      hover:bg-blue-400 hover:text-white group transition-colors dark:border dark:border-gray-600 ">
              <img src="/assets/icons/write.svg"
                   class="size-3.5 invert dark:invert-0 group-hover:invert group-hover:brightness-0 group-hover:contrast-200"
                   alt="">
            </a>
            <a href=""
               class="shadow w-max rounded-full p-1 px-2 flex items-center gap-1
                      hover:bg-blue-400 hover:text-white group transition-colors dark:border dark:border-gray-600 ">
              <img src="/assets/icons/pin.svg"
                   class="size-3.5 invert dark:invert-0 group-hover:invert group-hover:brightness-0 group-hover:contrast-200"
                   alt="">
            </a>
          </div>
        </div>
      </a>
    </div>
  </ng-container>
</div>

<!-- Column View -->
<div *ngIf="viewMode === 'columns' && !loading" class="w-full text-xs">
  <ng-container *ngFor="let thread of filteredThreads; let i = index;">
    <a [routerLink]="['/thread', user.role === 'teacher' ? thread.id : thread.thread.id]">
      <div class="w-full p-3 hover:shadow-md duration-200 transition-all dark:bg-[#0d1117] mb-1
      border-b border-b-gray-200 dark:border-b-gray-700 rounded-lg">
        <div class="grid xl:grid-cols-3 lg:grid-cols-2 items-center">
          <div>
            <div class="flex items-center gap-2">
              <div class="font-medium">{{ i + 1 }}.</div>
              <div>
                <div class="font-medium">{{ thread.course?.title | slice:0:24 }}..</div>
              </div>
            </div>
          </div>
          <div>{{ user.role === 'teacher' ? thread.title : thread.thread.title }}</div>
          <div class="text-end">
            <ng-container *ngIf="user.role === 'student'">
              {{ 'COURSES.assignments_to_do' | translate }}: <span class="font-mono">10</span>
            </ng-container>
            <ng-container *ngIf="user.role === 'teacher'">
              {{ 'COURSES.students' | translate }}: <span class="font-mono">{{ thread.booked_slots }}/{{ thread.max_students }}</span>
            </ng-container>
          </div>
        </div>
      </div>
    </a>
  </ng-container>
</div>

<!-- No Results from Search -->
<div *ngIf="noResults && searchQuery && !loading" class="w-full text-sm text-gray-600 mb-32 mt-2 text-center p-8">
  <div class="flex flex-col items-center justify-center">
    <img src="/assets/icons/search.svg" class="size-12 opacity-30 dark:invert mb-4" alt="Search">
    <div class="font-medium mb-2">{{ 'COURSES.nothing_found' | translate }}</div>
    <div class="text-xs">
      {{ 'COURSES.no_results_for_query' | translate }} "{{ searchQuery }}". {{ 'COURSES.try_different_query' | translate }}
    </div>
  </div>
</div>

<!-- Loading Indicator -->
<div *ngIf="loading" class="w-full text-sm text-gray-600 mb-32 mt-2 text-center p-8">
  <div class="flex flex-col items-center justify-center">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
    <div class="font-medium">{{ 'COURSES.loading_courses' | translate }}</div>
  </div>
</div>

<!-- No Courses -->
<div *ngIf="user_threads.length === 0 && !searchQuery && !loading" class="w-full text-sm text-gray-600 mb-32 mt-2">
  <ng-container>
    <div *ngIf="user.role == 'student'">
      <div class="flex flex-wrap items-center gap-2">
        <div>
          {{ 'COURSES.not_registered_for_any_course' | translate }}
        </div>
        <img src="/assets/icons/sad.svg" class="size-5" alt="">
      </div>
      <div>
        <a class="text-blue-500" href="/register"> {{ 'COURSES.register_here' | translate }}</a>
      </div>
    </div>
    <div *ngIf="user.role == 'teacher'">
      <div class="flex flex-wrap items-center gap-2">
        <div>
          {{ 'COURSES.teacher_no_courses' | translate }}
        </div>
      </div>
    </div>
  </ng-container>
</div>

