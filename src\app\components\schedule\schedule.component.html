<app-breadcrumbs [items]="breadcrumbs"/>
<div class="max-w-screen-xl mt-4 mb-5">
  <!-- Role indicator -->
  <div class="mb-3">
    <span class="text-sm font-medium px-3 py-1 rounded-full"
          [ngClass]="{'bg-blue-100 text-blue-800': currentUser.role === 'student',
                      'bg-green-100 text-green-800': currentUser.role === 'teacher'}">
      {{ (currentUser.role === 'student' ? 'SCHEDULE.student_schedule' : 'SCHEDULE.teacher_schedule') | translate }}
    </span>
  </div>

  <!-- Week navigation -->
  <div class="flex justify-between items-center mb-4">
    <button (click)="previousWeek()" class="px-2 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
      {{ 'SCHEDULE.previous_week' | translate }}
    </button>

    <div class="text-lg font-medium">
      {{ startDate | date:'d MMM' }} - {{ endDate | date:'d MMM, y' }}
    </div>

    <button (click)="nextWeek()" class="px-2 py-1 text-xs bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center">
      {{ 'SCHEDULE.next_week' | translate }}
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
      </svg>
    </button>
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoading" class="flex justify-center py-8">
    <div class="spinner rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </div>

  <!-- Error message -->
  <div *ngIf="error && !isLoading" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
    {{ error | translate }}
  </div>

  <!-- No schedule data message -->
  <div *ngIf="!isLoading && (!scheduleData || !scheduleData.schedules || scheduleData.schedules.length === 0)" class="bg-gray-50 border border-gray-200 text-gray-700 px-4 py-8 rounded-lg mb-4 text-center">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
    </svg>
    <p class="text-lg font-medium">
      {{ (currentUser.role === 'student' ? 'SCHEDULE.no_classes_this_week_student' : 'SCHEDULE.no_classes_this_week_teacher') | translate }}
    </p>
    <p class="text-sm text-gray-500 mt-1">
      {{ (currentUser.role === 'student' ? 'SCHEDULE.try_different_week_student' : 'SCHEDULE.try_different_week_teacher') | translate }}
    </p>
  </div>

  <!-- Schedule grid -->
  <div *ngIf="!isLoading && weekSchedule.length > 0" class="grid xl:grid-cols-2 gap-5">
    <!-- Day card for each day of the week -->
    <div *ngFor="let day of weekSchedule" class="shadow border rounded-md p-3">
      <div class="flex justify-between items-center">
        <div class="font-medium mt-2 mb-2 text-sm text-blue-700 dark:text-blue-400">
          {{ day.day | translate }}
        </div>
        <div class="text-xs text-gray-500">
          {{ day.date | date:'d MMMM' }}
        </div>
      </div>

      <!-- No classes for this day message -->
      <div *ngIf="day.items.length === 0" class="text-gray-500 text-sm py-4 text-center">
        {{ 'SCHEDULE.no_classes_today' | translate }}
      </div>

      <!-- Schedule items for this day -->
      <div *ngIf="day.items.length > 0" class="text-gray-800 mb-2 text-sm font-gotham">
        <div *ngFor="let item of day.items" class="grid grid-cols-5 border-b border-b-gray-150 mb-3">
          <div class="p-1">{{ formatTime(item.start_time) }} - {{ formatTime(item.end_time) }}</div>
          <div class="p-1">{{ getLocationName(item) }}</div>

          <!-- Class type -->
          <div *ngIf="item.type === 'class'" class="col-span-3 text-black dark:text-white p-1">
            <div class="font-medium">{{ item.course_name }}</div>
            <div class="text-xs text-gray-500">{{ item.thread_name }}</div>
            <!-- Show additional info for teachers -->
            <div *ngIf="currentUser.role === 'teacher'" class="text-xs mt-1">
              <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                {{ item.booked_slots || 0 }}/{{ item.max_students || 25 }} {{ 'SCHEDULE.students' | translate }}
              </span> 
            </div>
          </div>

          <!-- Sport type -->
          <div *ngIf="item.type === 'sport'" class="col-span-3 text-black dark:text-white p-1">
            <div class="font-medium">{{ item.sport_type }}</div>
            <div class="text-xs">
              <span [ngClass]="{
                'text-green-600': item.status === 'CONFIRMED',
                'text-yellow-600': item.status === 'PENDING',
                'text-red-600': item.status === 'CANCELLED'
              }">
                {{ (item.status === 'CONFIRMED' ? 'SCHEDULE.confirmed' :
                   item.status === 'PENDING' ? 'SCHEDULE.pending' :
                   item.status === 'CANCELLED' ? 'SCHEDULE.cancelled' : 'SCHEDULE.unknown_status') | translate }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
