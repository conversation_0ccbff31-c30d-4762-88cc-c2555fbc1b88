import {Component, Inject} from '@angular/core';
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {WeekAddDialogComponent} from "../week-add-dialog/week-add-dialog.component";

@Component({
  selector: 'app-assignment-group-add-dialog',
  templateUrl: './assignment-group-add-dialog.component.html',
  styleUrl: './assignment-group-add-dialog.component.css'
})
export class AssignmentGroupAddDialogComponent {
  assignmentGroupForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<WeekAddDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public threadId: number
  ) {
    this.assignmentGroupForm = this.fb.group({
      name: ['', Validators.required],
      group_type: ['', Validators.required],
      weight: [
        null,
        [Validators.required, Validators.min(0.001), Validators.max(0.9)]
      ],
    });
  }

  submit() {
    if (this.assignmentGroupForm.invalid) return;

    const formData = this.assignmentGroupForm.value;
    formData.weight = parseFloat(formData.weight);  // Преобразуем в число

    this.dialogRef.close(formData);
  }

  cancel() {
    this.dialogRef.close(); // отмена
  }
}
