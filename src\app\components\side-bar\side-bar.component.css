.sidebar {
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--border-color);
  transition: background-color 0.3s, border-color 0.3s, width 0.3s;
}

/* Style for active menu items */
.bg-primary-100.text-primary-600 {
  background-color: var(--primary-100);
  color: var(--primary-600);
}

/* Dark mode active menu items */
.dark .bg-primary-100.text-primary-600 {
  background-color: var(--primary-900);
  color: var(--primary-300);
}

/* Hide scrollbar but allow scrolling */
.overflow-y-auto::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.overflow-y-auto {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* SVG icon styles */
svg {
  transition: color 0.3s;
}

/* Dark mode text colors */
.dark .text-tertiary {
  color: var(--text-tertiary);
}

/* Improve contrast for menu items in dark mode */
.dark a:hover {
  color: var(--text-primary);
}

/* Improve active item visibility in dark mode */
.dark a.bg-primary-900 svg {
  color: var(--primary-300);
}
