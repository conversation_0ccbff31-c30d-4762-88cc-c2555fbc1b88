import {Component, Input} from '@angular/core';
import {TranslateModule} from '@ngx-translate/core';
import {RouterModule} from '@angular/router';
import {CommonModule} from '@angular/common';

@Component({
  selector: 'app-breadcrumbs',
  templateUrl: './breadcrumbs.component.html',
  styleUrl: './breadcrumbs.component.css',
  imports: [TranslateModule, RouterModule, CommonModule],
  standalone: true
})
export class BreadcrumbsComponent {
  @Input() items: { label: string, url?: string }[] = [];
}
