import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-week-delete-dialog',
  templateUrl: './week-delete-dialog.component.html',
  styleUrl: './week-delete-dialog.component.css'
})
export class WeekDeleteDialogComponent {
  captchaInput: string = '';
  captchaText: string = 'DELETE';

  constructor(
    private dialogRef: MatDialogRef<WeekDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { week: any }
  ) {}

  confirm(): void {
    if (this.captchaInput === this.captchaText) {
      this.dialogRef.close(true);
    }
  }

  cancel(): void {
    this.dialogRef.close(false);
  }
}
