import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-news-list',
  templateUrl: './news-list.component.html',
  styleUrls: ['./news-list.component.css']
})
export class NewsListComponent {
  @Input() newsList: any[] = [];  // Список новостей
  @Input() limit: number | null = null;  // Лимит количества новостей (по умолчанию все)
  @Input() showDeleteButton: boolean = false;  // Показывать ли кнопку удаления
  @Input() showContent: boolean = false;  // Показывать ли кнопку удаления
  @Output() onDelete = new EventEmitter<number>();  // Событие удаления

  deletePost(id: number) {
    this.onDelete.emit(id);
  }

  get displayedNews() {
    return this.limit ? this.newsList.slice(0, this.limit) : this.newsList;
  }
}
