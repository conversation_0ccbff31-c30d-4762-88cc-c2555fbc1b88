import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environments } from '../../environments/environments';

export interface SemesterBreak {
  id: number;
  semester_id: number;
  break_date: {
    seconds: number;
  };
  description: string;
  created_at: {
    seconds: number;
    nanos: number;
  };
  updated_at: {
    seconds: number;
    nanos: number;
  };
}

export interface Semester {
  id: number;
  name: string;
  start_date: {
    seconds: number;
  };
  end_date: {
    seconds: number;
  };
  created_at: {
    seconds: number;
    nanos: number;
  };
  updated_at: {
    seconds: number;
    nanos: number;
  };
  breaks: SemesterBreak[] | null;
}

export interface SemesterResponse {
  semesters: Semester[];
}

@Injectable({
  providedIn: 'root'
})
export class SemesterService {
  private apiUrl = environments.API;

  constructor(private http: HttpClient) {}

  /**
   * Get all semesters with their breaks
   * @returns Observable with semesters and their breaks
   */
  getSemestersWithBreaks(): Observable<SemesterResponse> {
    return this.http.get<SemesterResponse>(`${this.apiUrl}/semester/with-breaks`);
  }

  /**
   * Get a specific semester by ID
   * @param id Semester ID
   * @returns Observable with semester details
   */
  getSemesterById(id: number): Observable<Semester> {
    return this.http.get<Semester>(`${this.apiUrl}/semester/${id}`);
  }

  /**
   * Convert timestamp seconds to Date object
   * @param seconds Timestamp in seconds
   * @returns JavaScript Date object
   */
  convertTimestampToDate(seconds: number): Date {
    return new Date(seconds * 1000);
  }
}
