import { Component, OnInit } from '@angular/core';
import { DateAdapter } from '@angular/material/core';
import { SemesterService, Semester, SemesterBreak } from '../../core/services/semester.service';
import { finalize } from 'rxjs/operators';

interface CalendarEvent {
  date: Date;
  title: string;
  description: string;
  type: string;
  semesterId?: number;
  breakId?: number;
}

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
  styleUrl: './calendar.component.css'
})
export class CalendarComponent implements OnInit {
  breadcrumbs: { label: string, url?: string }[] = [];
  selected: Date = new Date();

  // Academic events from API
  academicEvents: CalendarEvent[] = [];

  // Loading state
  isLoading: boolean = false;

  // Error state
  loadError: string | null = null;

  // Filtered events based on selected date
  filteredEvents: CalendarEvent[] = [];

  // Filter options
  filterOptions = [
    { value: 'all', label: 'All Events' },
    { value: 'semester', label: 'Semester Events' },
    { value: 'exam', label: 'Exams' },
    { value: 'registration', label: 'Registration' },
    { value: 'holiday', label: 'Holidays' }
  ];

  selectedFilter: string = 'all';

  constructor(
    private dateAdapter: DateAdapter<Date>,
    private semesterService: SemesterService
  ) {}

  ngOnInit() {
    this.breadcrumbs = [
      { label: 'Главная', url: '/' },
      { label: 'Академический календарь' },
    ];

    // Set the locale for the date adapter
    this.dateAdapter.setLocale('ru-RU');

    // Load semester data from API
    this.loadSemesterData();
  }

  // Load semester data from API
  loadSemesterData() {
    this.isLoading = true;
    this.loadError = null;

    this.semesterService.getSemestersWithBreaks()
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: (response) => {
          if (response && response.semesters) {
            this.processApiData(response.semesters);
          } else {
            this.loadError = 'Invalid API response format';
          }
        },
        error: (error) => {
          console.error('Error loading semester data:', error);
          this.loadError = 'Failed to load calendar data. Please try again later.';
          // Load fallback data if API fails
          this.loadFallbackData();
        }
      });
  }

  // Process API data and convert to calendar events
  processApiData(semesters: Semester[]) {
    const events: CalendarEvent[] = [];

    semesters.forEach(semester => {
      // Add semester start event
      events.push({
        date: this.semesterService.convertTimestampToDate(semester.start_date.seconds),
        title: `${semester.name} Begins`,
        description: `First day of classes for ${semester.name}`,
        type: 'semester',
        semesterId: semester.id
      });

      // Add semester end event
      events.push({
        date: this.semesterService.convertTimestampToDate(semester.end_date.seconds),
        title: `${semester.name} Ends`,
        description: `Last day of classes for ${semester.name}`,
        type: 'semester',
        semesterId: semester.id
      });

      // Add breaks if they exist
      if (semester.breaks && semester.breaks.length > 0) {
        semester.breaks.forEach(breakItem => {
          events.push({
            date: this.semesterService.convertTimestampToDate(breakItem.break_date.seconds),
            title: breakItem.description,
            description: `Break day for ${semester.name}`,
            type: 'holiday',
            semesterId: semester.id,
            breakId: breakItem.id
          });
        });
      }
    });

    this.academicEvents = events;
    this.filterEvents();
  }

  // Load fallback data if API fails
  loadFallbackData() {
    this.academicEvents = [
      {
        date: new Date(2024, 8, 1),
        title: 'Fall Semester Begins',
        description: 'First day of classes for the Fall semester',
        type: 'semester'
      },
      {
        date: new Date(2024, 9, 15),
        title: 'Midterm Week',
        description: 'Midterm examinations for all courses',
        type: 'exam'
      },
      {
        date: new Date(2024, 10, 1),
        title: 'Registration for Spring Semester',
        description: 'Registration opens for the Spring semester',
        type: 'registration'
      },
      {
        date: new Date(2024, 11, 20),
        title: 'Winter Break',
        description: 'Winter break begins',
        type: 'holiday'
      },
      {
        date: new Date(2025, 0, 15),
        title: 'Classes Resume',
        description: 'Classes resume after winter break',
        type: 'semester'
      },
      {
        date: new Date(2025, 1, 1),
        title: 'Final Exams',
        description: 'Final examinations for the Fall semester',
        type: 'exam'
      },
      {
        date: new Date(2025, 1, 15),
        title: 'Spring Semester Begins',
        description: 'First day of classes for the Spring semester',
        type: 'semester'
      }
    ];

    this.filterEvents();
  }

  // Filter events based on selected date and filter type
  filterEvents() {
    if (this.selectedFilter === 'all') {
      this.filteredEvents = [...this.academicEvents];
    } else {
      this.filteredEvents = this.academicEvents.filter(event => event.type === this.selectedFilter);
    }

    // Sort events by date
    this.filteredEvents.sort((a, b) => a.date.getTime() - b.date.getTime());
  }

  // Handle date selection
  onDateSelected(event: any) {
    this.selected = event;
    this.filterEvents();
  }

  // Handle filter change
  onFilterChange(filter: string) {
    this.selectedFilter = filter;
    this.filterEvents();
  }

  // Format date for display
  formatDate(date: Date): string {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  // Get event type class for styling
  getEventTypeClass(type: string): string {
    switch(type) {
      case 'semester': return 'bg-blue-100 text-blue-800';
      case 'exam': return 'bg-red-100 text-red-800';
      case 'registration': return 'bg-green-100 text-green-800';
      case 'holiday': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }
}
