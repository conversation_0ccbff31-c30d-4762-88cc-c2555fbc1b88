import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, of, throwError } from 'rxjs';
import { environments } from '../../environments/environments';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class SubmissionService {
  private apiUrl = environments.API;

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Submit an assignment (legacy method using pre-uploaded file URLs)
   * @param assignmentId The ID of the assignment
   * @param fileUrls Array of file URLs
   * @param comment User comment
   * @returns Observable with submission details
   */
  submitAssignment(
    assignmentId: number,
    fileUrls: string[],
    comment: string
  ): Observable<any> {
    // Get the current user from the auth service
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      throw new Error('User not authenticated');
    }

    return this.http.post<any>(
      `${this.apiUrl}/assignments/${assignmentId}/submissions`,
      {
        user_id: currentUser.id,
        file_urls: fileUrls,
        comment: comment
      }
    );
  }

  /**
   * Submit an assignment with direct file upload
   * @param assignmentId The ID of the assignment
   * @param file The file to upload
   * @param comment User comment
   * @returns Observable with submission details
   */
  submitAssignmentWithFile(
    assignmentId: number,
    file: File | null,
    comment: string
  ): Observable<any> {
    // Get the current user from the auth service
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      throw new Error('User not authenticated');
    }

    const formData = new FormData();
    formData.append('user_id', currentUser.id.toString());
    formData.append('comment', comment);

    if (file) {
      formData.append('file', file);
    }

    return this.http.post<any>(
      `${this.apiUrl}/assignments/${assignmentId}/submissions`,
      formData
    );
  }

  /**
   * Grade a submission
   * @param submissionId The ID of the submission
   * @param score The score to assign
   * @param feedback Feedback for the student
   * @returns Observable with the graded submission details
   */
  gradeSubmission(
    submissionId: number,
    score: number,
    feedback: string
  ): Observable<any> {
    // Get the current user from the auth service
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      throw new Error('User not authenticated');
    }

    // Check if user is a teacher
    if (currentUser.role !== 'teacher' && currentUser.role !== 'admin') {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can grade submissions'));
    }

    return this.http.post<any>(
      `${this.apiUrl}/teacher/assignments/${submissionId}/grade`,
      {
        score: score,
        feedback: feedback
      }
    );
  }

  /**
   * Upload a file for submission
   * @param file The file to upload
   * @returns Observable with the uploaded file URL
   */
  uploadFile(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);

    return this.http.post<any>(
      `${this.apiUrl}/storage/upload`,
      formData
    );
  }

  /**
   * Get submissions for an assignment
   * @param assignmentId The ID of the assignment
   * @returns Observable with submission details
   */
  getSubmissions(assignmentId: number): Observable<any> {
    return this.http.get<any>(
      `${this.apiUrl}/assignments/${assignmentId}/submissions`
    ).pipe(
      // Handle empty responses or errors gracefully
      catchError(error => {
        // If it's a 404 (not found), return an empty array
        if (error.status === 404) {
          return of([]);
        }
        // Otherwise, re-throw the error
        return throwError(() => error);
      })
    );
  }
}
