import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {WithSidebarLayoutComponent} from "./layout/with-sidebar-layout/with-sidebar-layout.component";
import {NoSidebarLayoutComponent} from "./layout/no-sidebar-layout/no-sidebar-layout.component";
import {NotFoundComponent} from "./components/not-found/not-found.component";
import {MainComponent} from "./components/main/main.component";
import {LoginComponent} from "./components/login/login.component";
import {NewsComponent} from "./components/news/news.component";
import {NewsDetailComponent} from "./components/news-detail/news-detail.component";
import {RegisterComponent} from "./components/register/register.component";
import {CoursesComponent} from "./components/courses/courses.component";
import {CourseDetailComponent} from "./components/course-detail/course-detail.component";
import {AssignmentDetailComponent} from "./components/assignment-detail/assignment-detail.component";
import {ProfileComponent} from "./components/profile/profile.component";
import {ScheduleComponent} from "./components/schedule/schedule.component";
import {RatingsComponent} from "./components/ratings/ratings.component";
import {TasksComponent} from "./components/tasks/tasks.component";
import {AddTaskComponent} from "./components/add-task/add-task.component";
import {authGuard} from "./core/guards/auth.guard";
import {redirectIfLoggedInGuard} from "./core/guards/redirect-if-logged-in.guard";
import {SetRatingComponent} from "./components/teacher/set-rating/set-rating.component";
import {teacherRoleGuard} from "./core/guards/teacher-role.guard";
import {studentRoleGuard} from "./core/guards/student-role.guard";
import {ExamScheduleComponent} from "./components/exam-schedule/exam-schedule.component";
import {CalendarComponent} from "./components/calendar/calendar.component";
import {DocumentsComponent} from "./components/documents/documents.component";

const routes: Routes = [
  {
    path: '',
    component: WithSidebarLayoutComponent,
    children: [
      {
        path: '',
        component: MainComponent,
        data: { breadcrumb: 'BREADCRUMB.HOME' }
      },
      {
        path: 'news',
        component: NewsComponent,
        data: { breadcrumb: 'BREADCRUMB.NEWS' }
      },
      {
        path: 'profile',
        component: ProfileComponent,
        data: { breadcrumb: 'BREADCRUMB.PROFILE' }
      },
      {
        path: 'courses',
        component: CoursesComponent,
        data: { breadcrumb: 'BREADCRUMB.COURSES' }
      },
      {
        path: 'ratings',
        component: RatingsComponent,
        data: { breadcrumb: 'BREADCRUMB.RATINGS' },
        canActivate: [studentRoleGuard]
      },
      {
        path: 'tasks',
        component: TasksComponent,
        data: { breadcrumb: 'BREADCRUMB.TASKS' }
      },
      {
        path: 'add-task',
        component: AddTaskComponent,
        data: { breadcrumb: 'BREADCRUMB.ADD_TASK' },
        canActivate: [teacherRoleGuard]
      },
      {
        path: 'set-ratings',
        component: SetRatingComponent,
        data: { breadcrumb: 'BREADCRUMB.SET_RATINGS' },
        canActivate: [teacherRoleGuard]
      },
      {
        path: 'schedule',
        component: ScheduleComponent,
        data: { breadcrumb: 'BREADCRUMB.SCHEDULE' }
      },
      {
        path: 'thread/:id',
        component: CourseDetailComponent,
        data: { breadcrumb: 'BREADCRUMB.COURSE_DETAILS' }
      },
      {
        path: 'thread/:threadId/assignments/:id',
        component: AssignmentDetailComponent,
        data: { breadcrumb: 'BREADCRUMB.ASSIGNMENT_DETAILS' }
      },
      {
        path: 'news/:id',
        component: NewsDetailComponent,
        data: { breadcrumb: 'BREADCRUMB.NEWS_DETAILS' }
      },
      {
        path: 'register',
        component: RegisterComponent,
        data: { breadcrumb: 'BREADCRUMB.REGISTRATION' },
        canActivate: [studentRoleGuard]
      },
      {
        path: 'academic/exam-schedule',
        component: ExamScheduleComponent,
        data: { breadcrumb: 'ACADEMIC.EXAM' },
      },
      {
        path: 'academic/calendar',
        component: CalendarComponent,
        data: { breadcrumb: 'ACADEMIC.CALENDAR' },
      },
      {
        path: 'documents/docs',
        component: DocumentsComponent,
        data: { breadcrumb: 'BREADCRUMB.DOCUMENTS' },
      },
    ],
    canActivate: [authGuard]
  },
  {
    path: '',
    component: NoSidebarLayoutComponent,
    children: [
      {
        path: 'login',
        loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent)
      },
    ],
    canActivate: [redirectIfLoggedInGuard]
  },

  {path: '**', component: NotFoundComponent},
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {
}
