import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, of, map } from 'rxjs';
import { environments } from '../../environments/environments';
import { AuthService } from './auth.service';
import { StorageService } from './storage.service';

export interface Schedule {
  id: number;
  thread_id: number;
  day_of_week: number;
  start_time: string;
  end_time: string;
  created_at: {
    seconds: number;
    nanos: number;
  };
  updated_at: {
    seconds: number;
    nanos: number;
  };
}

export interface Course {
  id: number;
  title: string;
  description: string;
}

export interface Semester {
  id: number;
  name: string;
  start_date: {
    seconds: number;
  };
  end_date: {
    seconds: number;
  };
}

export interface Teacher {
  id: number;
  name: string;
  surname: string;
  email: string;
}

export interface Thread {
  id: number;
  course_id: number;
  semester_id: number;
  teacher_id: number;
  title: string;
  max_students: number;
  available_slots?: number;
  booked_slots?: number;
  syllabus_url?: string;
  created_at: {
    seconds: number;
    nanos: number;
  };
  updated_at: {
    seconds: number;
    nanos: number;
  };
}

export interface StudentThreadResponse {
  threads: {
    thread: Thread;
    course: Course;
    semester: Semester;
    teacher: Teacher;
    schedules: Schedule[] | null;
  }[];
}

export interface TeacherThreadResponse {
  teacher: {
    email: string;
    id: number;
    name: string;
    surname: string;
  };
  threads: {
    id: number;
    course_id: number;
    semester_id: number;
    teacher_id: number;
    title: string;
    max_students: number;
    available_slots?: number;
    booked_slots?: number;
    course: Course;
    schedules: Schedule[] | null;
    syllabus_url?: string;
    created_at: {
      seconds: number;
      nanos: number;
    };
    updated_at: {
      seconds: number;
      nanos: number;
    };
  }[];
}

export interface ExamDisplay {
  id: number;
  thread_id: number;
  course_title: string;
  course_id: number;
  semester_name: string;
  semester_id: number;
  teacher_name: string;
  teacher_id: number;
  stream_title: string;
  schedules: {
    day: number;
    startTime: string;
    endTime: string;
  }[] | null;
  exam_status: 'not_scheduled' | 'scheduled';
  exam_date?: Date;
  exam_location?: string;
  exam_type?: string;
  exam_duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class ExamService {
  private apiUrl = environments.API;

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private storageService: StorageService
  ) {}

  /**
   * Get threads for a student with schedules
   * @returns Observable with student's threads and schedules
   */
  getStudentThreads(): Observable<ExamDisplay[]> {
    // Get the current user from the auth service
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      throw new Error('User not authenticated');
    }

    return this.http.get<StudentThreadResponse>(
      `${this.apiUrl}/thread/user/${currentUser.id}/with-schedules`
    ).pipe(
      map(response => {
        if (!response || !response.threads || !Array.isArray(response.threads)) {
          return [];
        }

        return response.threads.map(item => {
          const schedules = item.schedules ? item.schedules.map(schedule => ({
            day: schedule.day_of_week,
            startTime: schedule.start_time,
            endTime: schedule.end_time
          })) : null;

          return {
            id: item.thread.id,
            thread_id: item.thread.id,
            course_title: item.course.title,
            course_id: item.course.id,
            semester_name: item.semester.name,
            semester_id: item.semester.id,
            teacher_name: `${item.teacher.name} ${item.teacher.surname}`,
            teacher_id: item.teacher.id,
            stream_title: item.thread.title,
            schedules: schedules,
            exam_status: 'not_scheduled' as 'not_scheduled' // Currently no exam data available
          };
        });
      }),
      catchError(error => {
        console.error('Error fetching student threads:', error);
        return of([]);
      })
    );
  }

  /**
   * Get threads for a teacher
   * @returns Observable with teacher's threads
   */
  getTeacherThreads(): Observable<ExamDisplay[]> {
    // Get token for authorization
    const token = this.storageService.getToken();
    const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);

    return this.http.get<TeacherThreadResponse>(
      `${this.apiUrl}/teacher/my-threads`, { headers }
    ).pipe(
      map(response => {
        if (!response || !response.threads || !Array.isArray(response.threads)) {
          return [];
        }

        return response.threads.map(thread => {
          const schedules = thread.schedules ? thread.schedules.map(schedule => ({
            day: schedule.day_of_week,
            startTime: schedule.start_time,
            endTime: schedule.end_time
          })) : null;

          return {
            id: thread.id,
            thread_id: thread.id,
            course_title: thread.course.title,
            course_id: thread.course_id,
            semester_name: '', // Not provided in teacher response
            semester_id: thread.semester_id,
            teacher_name: `${response.teacher.name} ${response.teacher.surname}`,
            teacher_id: thread.teacher_id,
            stream_title: thread.title,
            schedules: schedules,
            exam_status: 'not_scheduled' as 'not_scheduled' // Currently no exam data available
          };
        });
      }),
      catchError(error => {
        console.error('Error fetching teacher threads:', error);
        return of([]);
      })
    );
  }

  /**
   * Convert timestamp seconds to Date object
   * @param seconds Timestamp in seconds
   * @returns JavaScript Date object
   */
  convertTimestampToDate(seconds: number): Date {
    return new Date(seconds * 1000);
  }

  /**
   * Get day of week name
   * @param day Day number (1-7, where 1 is Monday)
   * @returns Day name in Russian
   */
  getDayOfWeekName(day: number): string {
    const days = [
      'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота', 'Воскресенье'
    ];
    return days[day - 1] || 'Неизвестно';
  }
}
