import {Injectable} from '@angular/core';
import {environments} from "../../environments/environments";
import {HttpClient, HttpParams} from "@angular/common/http";
import {Router} from "@angular/router";
import {StorageService} from "./storage.service";
import {Observable, throwError} from "rxjs";

@Injectable({
  providedIn: 'root'
})
export class WeekService {
  private apiUrl = environments.API

  constructor(
    private http: HttpClient,
    private router: Router,
    private storage: StorageService
  ) {
  }

  private checkTeacherRole(): boolean {
    const user = this.storage.getUser();
    return user && (user.role === 'teacher' || user.role === 'admin');
  }

  createWeek(thread_id: number, week_number: number, type: string, title: string, description: string) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can create weeks'));
    }

    return this.http.post<{ message: string } | { error: string }>(
      `${this.apiUrl}/thread/week`,
      {thread_id, week_number, type, title, description}
    );
  }

  getAllWeeksByThread(thread_id: number) {
    return this.http.get<{ message: string }>(`${this.apiUrl}/thread/week?thread_id=${thread_id}`);
  }


  getThreadHomework(thread_id: number, userId: number): Observable<any> {
    const params = new HttpParams().set('user_id', userId);
    return this.http.get<any>(`${this.apiUrl}/thread/${thread_id}/weeks-hw`, {params});
  }

  deleteWeek(week_id: number) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can delete weeks'));
    }

    return this.http.delete<any>(`${this.apiUrl}/thread/week/${week_id}`);
  }

  updateWeek(week_id: number, thread_id: number, week_number: number, type: string, title: string, description: string) {
    if (!this.checkTeacherRole()) {
      return throwError(() => new Error('Unauthorized: Only teachers and admins can update weeks'));
    }

    return this.http.put<any>(`${this.apiUrl}/thread/week/${week_id}`, {
      thread_id, week_number, type, title, description
    });
  }
}
