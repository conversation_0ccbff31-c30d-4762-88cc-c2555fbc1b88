.card {
  width: 300px; /* Задайте желаемую ширину карточки */
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden; /* Обрезает волну, чтобы она не выходила за границы */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: sans-serif;
}

.card-header {
  background-color: #f0f8ff; /* Светлый фон заголовка */
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-status {
  font-weight: bold;
  color: #007bff; /* Цвет текста "Завершено" */
}

.card-date {
  color: #666;
}

.card-wave {
  position: relative;
  height: 50px; /* Высота волны */
  background: linear-gradient(to bottom, #f0f8ff, white); /* Градиент для создания эффекта волны */
  clip-path: ellipse(150% 50% at 50% 100%); /* Создает форму волны */
}

.card-content {
  padding: 20px;
}

.card-title {
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: 10px;
}

.card-info {
  display: flex;
  justify-content: space-around;
}

.card-info-item {
  background-color: #eee;
  padding: 8px 12px;
  border-radius: 4px;
}
