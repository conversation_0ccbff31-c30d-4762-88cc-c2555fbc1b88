/* Main page styles */

/* Ensure proper scrolling behavior */
:host {
  display: block;
  width: 100%;
}

/* Hero section animation */
.card.bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Stats cards hover effect */
.card.border-l-4 {
  transition: all 0.3s ease;
}

.card.border-l-4:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Calendar customization */
.calendar-small .mat-calendar-body-cell {
  font-size: 0.75rem !important;
}

.calendar-small .mat-calendar-body-selected {
  background-color: var(--primary-600);
  color: white;
}

.calendar-small .mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: var(--primary-600);
}

/* Dark theme calendar customization */
:host-context(.dark) .calendar-small {
  color: var(--text-primary);
}

:host-context(.dark) .calendar-small .mat-calendar-body-cell-content {
  color: var(--text-primary);
}

:host-context(.dark) .calendar-small .mat-calendar-body-disabled > .mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
  color: var(--text-tertiary);
}

:host-context(.dark) .calendar-small .mat-calendar-arrow,
:host-context(.dark) .calendar-small .mat-calendar-next-button,
:host-context(.dark) .calendar-small .mat-calendar-previous-button {
  border-top-color: var(--text-primary);
  color: var(--text-primary);
}

:host-context(.dark) .calendar-small .mat-calendar-table-header,
:host-context(.dark) .calendar-small .mat-calendar-body-label {
  color: var(--text-secondary);
}

:host-context(.dark) .calendar-small .mat-calendar-body-selected {
  background-color: var(--primary-600);
  color: white;
}

:host-context(.dark) .calendar-small .mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: var(--primary-600);
}

/* Quick action buttons */
.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card.bg-gradient-to-r {
    padding: 1rem;
  }

  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  /* Stat cards responsive adjustments */
  .flex.justify-end.gap-4 {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-end;
  }
}

@media (max-width: 640px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  /* Stat cards responsive adjustments for small screens */
  .flex.justify-end.gap-4 {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .flex.justify-end.gap-4 .card {
    width: 100% !important;
    margin-bottom: 0.5rem;
  }
}