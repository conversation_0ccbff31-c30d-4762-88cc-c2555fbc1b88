import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AssignmentService } from '../../../core/services/assignment.service';
import { WeekService } from '../../../core/services/week.service';
import { AssignmentGroupAddDialogComponent } from '../assignment-group-add-dialog/assignment-group-add-dialog.component';
import { AssignmentGroupUpdateDialogComponent } from '../assignment-group-update-dialog/assignment-group-update-dialog.component';
import { AssignmentGroupDeleteDialogComponent } from '../assignment-group-delete-dialog/assignment-group-delete-dialog.component';
import { WeekAddDialogComponent } from '../week-add-dialog/week-add-dialog.component';
import { WeekUpdateDialogComponent } from '../week-update-dialog/week-update-dialog.component';
import { WeekDeleteDialogComponent } from '../week-delete-dialog/week-delete-dialog.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-teacher-management-panel',
  templateUrl: './teacher-management-panel.component.html',
  styleUrl: './teacher-management-panel.component.css'
})
export class TeacherManagementPanelComponent implements OnInit, OnChanges {
  @Input() threadID: number | null = null;
  @Input() userID: number | null = null;

  activeTab: string = 'assignment-groups';

  // Data
  assignmentGroups: any[] = [];
  weeks: any[] = [];

  constructor(
    private assignmentService: AssignmentService,
    private weekService: WeekService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    if (this.threadID && this.userID) {
      this.loadData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if ((changes['threadID'] || changes['userID']) && this.threadID && this.userID) {
      this.loadData();
    }
  }

  loadData(): void {
    if (this.threadID && this.userID) {
      this.loadAssignmentGroups();
      this.loadWeeks();
    }
  }

  // Tab Navigation
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  // Assignment Groups Management
  loadAssignmentGroups(): void {
    if (this.threadID) {
      this.assignmentService.getListAssignmentGroupForThread(this.threadID).subscribe({
        next: (data) => {
          this.assignmentGroups = data || []; // Ensure it's never null
          console.log('Assignment Groups:', this.assignmentGroups);
        },
        error: (err) => {
          console.error('Error loading assignment groups:', err);
          this.assignmentGroups = []; // Set to empty array on error
        }
      });
    }
  }

  openAddGroupDialog(): void {
    if (!this.threadID) return;

    const dialogRef = this.dialog.open(AssignmentGroupAddDialogComponent, {
      width: '400px',
      data: this.threadID
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.threadID) {
        const {name, group_type, weight} = result;
        this.assignmentService.createAssignmentGroup(this.threadID, name, group_type, weight).subscribe({
          next: (res) => {
            console.log('Group created successfully:', res);
            this.snackBar.open('Группа заданий успешно создана', 'Закрыть', {
              duration: 6000,
              horizontalPosition: 'center',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-success']
            });
            this.loadAssignmentGroups();
          },
          error: (err) => {
            console.error('Error creating group:', err);
            this.snackBar.open('Ошибка при создании группы заданий', 'Закрыть', {
              duration: 6000,
              horizontalPosition: 'center',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-error']
            });
          }
        });
      }
    });
  }

  openUpdateGroupDialog(group: any): void {
    const dialogRef = this.dialog.open(AssignmentGroupUpdateDialogComponent, {
      width: '400px',
      data: { group, threadId: this.threadID }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.updateAssignmentGroup(result);
      }
    });
  }

  updateAssignmentGroup(groupData: any): void {
    this.assignmentService.updateAssignmentGroup(
      groupData.id,
      groupData.thread_id,
      groupData.name,
      groupData.group_type,
      groupData.weight
    ).subscribe({
      next: (res) => {
        console.log('Group updated successfully:', res);
        this.snackBar.open('Группа заданий успешно обновлена', 'Закрыть', {
          duration: 6000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: ['snackbar-success']
        });
        this.loadAssignmentGroups();
      },
      error: (err) => {
        console.error('Error updating group:', err);
        this.snackBar.open('Ошибка при обновлении группы заданий', 'Закрыть', {
          duration: 6000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  openDeleteGroupDialog(group: any): void {
    const dialogRef = this.dialog.open(AssignmentGroupDeleteDialogComponent, {
      width: '500px',
      data: { group }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteAssignmentGroup(group.id);
      }
    });
  }

  deleteAssignmentGroup(groupId: number): void {
    this.assignmentService.deleteAssignmentGroup(groupId).subscribe({
      next: (res) => {
        console.log('Group deleted successfully:', res);
        this.snackBar.open('Группа заданий успешно удалена', 'Закрыть', {
          duration: 6000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: ['snackbar-success']
        });
        this.loadAssignmentGroups();
        this.loadWeeks();
      },
      error: (err) => {
        console.error('Error deleting group:', err);
        this.snackBar.open('Ошибка при удалении группы заданий', 'Закрыть', {
          duration: 6000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  // Weeks Management
  loadWeeks(): void {
    if (this.threadID && this.userID) {
      this.weekService.getThreadHomework(this.threadID, this.userID).subscribe({
        next: data => {
          const rawWeeks = data.weeks;
          if (Array.isArray(rawWeeks)) {
            this.weeks = rawWeeks.map((item: any) => ({
              ...item.week,
              assignments: item.assignments || []
            }));
          } else {
            this.weeks = [];
          }
          console.log('Weeks:', this.weeks);
        },
        error: err => {
          console.error('Error loading weeks:', err);
        }
      });
    }
  }

  openAddWeekDialog(): void {
    if (!this.threadID) return;

    const dialogRef = this.dialog.open(WeekAddDialogComponent, {
      width: '400px',
      data: this.threadID
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.threadID) {
        const { week_number, type, title, description } = result;

        this.weekService.createWeek(this.threadID, week_number, type, title, description).subscribe({
          next: (res) => {
            console.log('Week created successfully:', res);
            this.snackBar.open('Week successfully created', 'Close', {
              duration: 6000,
              horizontalPosition: 'center',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-success']
            });
            this.loadWeeks();
          },
          error: (err) => {
            console.error('Error creating week:', err);
            this.snackBar.open('Error creating week', 'Close', {
              duration: 6000,
              horizontalPosition: 'center',
              verticalPosition: 'bottom',
              panelClass: ['snackbar-error']
            });
          }
        });
      }
    });
  }

  openUpdateWeekDialog(week: any): void {
    const dialogRef = this.dialog.open(WeekUpdateDialogComponent, {
      width: '400px',
      data: { week, threadId: this.threadID }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.updateWeek(result);
      }
    });
  }

  updateWeek(weekData: any): void {
    this.weekService.updateWeek(
      weekData.id,
      weekData.thread_id,
      weekData.week_number,
      weekData.type,
      weekData.title,
      weekData.description
    ).subscribe({
      next: (res) => {
        console.log('Week updated successfully:', res);
        this.snackBar.open('Week successfully updated', 'Close', {
          duration: 6000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: ['snackbar-success']
        });
        this.loadWeeks();
      },
      error: (err) => {
        console.error('Error updating week:', err);
        this.snackBar.open('Error updating week', 'Close', {
          duration: 6000,
          horizontalPosition: 'center',
          verticalPosition: 'bottom',
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  openDeleteWeekDialog(week: any): void {
    const dialogRef = this.dialog.open(WeekDeleteDialogComponent, {
      width: '500px',
      data: { week }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.deleteWeek(week.id);
      }
    });
  }

  deleteWeek(weekId: number): void {
    this.weekService.deleteWeek(weekId).subscribe({
      next: () => {
        this.snackBar.open('Week successfully deleted', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-success']
        });
        this.loadWeeks();
      },
      error: () => {
        this.snackBar.open('Error deleting week', 'Close', {
          duration: 3000,
          panelClass: ['snackbar-error']
        });
      }
    });
  }

  // Assignments Management
  navigateToAddTask(): void {
    this.router.navigate(['/add-task']);
  }
}
