import { Component, OnInit } from '@angular/core';
import { ScheduleService } from '../../core/services/schedule.service';
import { AuthService } from '../../core/services/auth.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { TranslateService } from '@ngx-translate/core';

interface ScheduleItem {
  type: string;
  thread_id?: number;
  thread_name?: string;
  course_name?: string;
  booking_id?: number;
  schedule_id?: number;
  start_time: string;
  end_time: string;
  location: string;
  sport_type?: string;
  status?: string;
  // Additional properties for teacher view
  booked_slots?: number;
  max_students?: number;
}

interface DaySchedule {
  day: string;
  date: Date;
  items: ScheduleItem[];
}

@Component({
  selector: 'app-schedule',
  templateUrl: './schedule.component.html',
  styleUrl: './schedule.component.css'
})
export class ScheduleComponent implements OnInit {
  breadcrumbs: { label: string, url?: string }[] = [];

  // Schedule data
  scheduleData: any = null;
  isLoading: boolean = false;
  error: string | null = null;

  // Organized schedule by day
  weekSchedule: DaySchedule[] = [];

  // Date range
  startDate: Date = new Date();
  endDate: Date = new Date();

  // Current user
  currentUser: any;

  // Day names translation keys
  dayNames: { [key: number]: string } = {
    0: 'SCHEDULE.sunday',
    1: 'SCHEDULE.monday',
    2: 'SCHEDULE.tuesday',
    3: 'SCHEDULE.wednesday',
    4: 'SCHEDULE.thursday',
    5: 'SCHEDULE.friday',
    6: 'SCHEDULE.saturday'
  };

  constructor(
    private scheduleService: ScheduleService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private translateService: TranslateService
  ) {
    this.currentUser = this.authService.getCurrentUser();

    // Set date range to current week (Monday to Sunday)
    this.setCurrentWeekDates();
  }

  ngOnInit() {
    this.breadcrumbs = [
      { label: 'BREADCRUMB.HOME', url: '/' },
      { label: 'BREADCRUMB.SCHEDULE' }
    ];

    // Fetch schedule for both students and teachers
    if (this.currentUser) {
      if (this.currentUser.role === 'student' || this.currentUser.role === 'teacher') {
        this.fetchSchedule();
      } else {
        this.error = 'SCHEDULE.only_students_teachers';
        this.snackBar.open(
          this.translateService.instant(this.error),
          this.translateService.instant('COMMON.close'),
          {
            duration: 5000,
            panelClass: ['snackbar-error']
          }
        );
      }
    } else {
      this.error = 'SCHEDULE.auth_required';
      this.snackBar.open(
        this.translateService.instant(this.error),
        this.translateService.instant('COMMON.close'),
        {
          duration: 5000,
          panelClass: ['snackbar-error']
        }
      );
    }
  }

  /**
   * Set date range to current week (Monday to Sunday)
   */
  setCurrentWeekDates(): void {
    const today = new Date();
    const day = today.getDay(); // 0 is Sunday, 1 is Monday, etc.

    // Calculate days to subtract to get to Monday
    const daysToMonday = day === 0 ? 6 : day - 1;

    // Set start date to Monday of current week
    this.startDate = new Date(today);
    this.startDate.setDate(today.getDate() - daysToMonday);
    this.startDate.setHours(0, 0, 0, 0);

    // Set end date to Sunday of current week
    this.endDate = new Date(this.startDate);
    this.endDate.setDate(this.startDate.getDate() + 6);
    this.endDate.setHours(23, 59, 59, 999);
  }

  /**
   * Fetch schedule data from the API
   */
  fetchSchedule(): void {
    this.isLoading = true;
    this.error = null;

    if (this.currentUser.role === 'teacher') {
      // For teachers, use the my-threads endpoint
      this.scheduleService.getTeacherThreads().subscribe({
        next: (data) => {
          // Transform the teacher threads data to match the schedule format
          this.scheduleData = this.transformTeacherThreadsToSchedule(data);
          this.organizeScheduleByDay();
          this.isLoading = false;
        },
        error: (err) => {
          console.error('Error fetching teacher schedule:', err);
          this.error = 'SCHEDULE.teacher_schedule_error';
          this.isLoading = false;
          this.snackBar.open(
            this.translateService.instant(this.error),
            this.translateService.instant('COMMON.close'),
            {
              duration: 5000,
              panelClass: ['snackbar-error']
            }
          );
        }
      });
    } else {
      // For students, use the existing endpoint with date range
      const startDateStr = this.startDate.toISOString();
      const endDateStr = this.endDate.toISOString();

      this.scheduleService.getStudentScheduleByDateRange(startDateStr, endDateStr).subscribe({
        next: (data) => {
          this.scheduleData = data;
          this.organizeScheduleByDay();
          this.isLoading = false;
        },
        error: (err) => {
          console.error('Error fetching student schedule:', err);
          this.error = 'SCHEDULE.student_schedule_error';
          this.isLoading = false;
          this.snackBar.open(
            this.translateService.instant(this.error),
            this.translateService.instant('COMMON.close'),
            {
              duration: 5000,
              panelClass: ['snackbar-error']
            }
          );
        }
      });
    }
  }

  /**
   * Transform teacher threads data to match the schedule format
   * @param data The teacher threads data from the API
   * @returns Transformed data in schedule format
   */
  transformTeacherThreadsToSchedule(data: any): any {
    if (!data || !data.threads || !Array.isArray(data.threads)) {
      return { schedules: [] };
    }

    const schedules: ScheduleItem[] = [];

    // Process each thread
    data.threads.forEach((thread: any) => {
      if (thread.schedules && Array.isArray(thread.schedules)) {
        // Process each schedule in the thread
        thread.schedules.forEach((schedule: any) => {
          // Convert day_of_week to a date within the current week
          const scheduleDate = new Date(this.startDate);
          // Adjust for day_of_week (1 = Monday, 7 = Sunday)
          // Need to adjust since our startDate is Monday (0) and API uses 1-based index
          const dayOffset = schedule.day_of_week - 1;
          scheduleDate.setDate(this.startDate.getDate() + dayOffset);

          // Create a date object for start time
          const startTimeParts = schedule.start_time.split(':');
          const startDate = new Date(scheduleDate);
          startDate.setHours(
            parseInt(startTimeParts[0], 10),
            parseInt(startTimeParts[1], 10),
            parseInt(startTimeParts[2] || '0', 10)
          );

          // Create a date object for end time
          const endTimeParts = schedule.end_time.split(':');
          const endDate = new Date(scheduleDate);
          endDate.setHours(
            parseInt(endTimeParts[0], 10),
            parseInt(endTimeParts[1], 10),
            parseInt(endTimeParts[2] || '0', 10)
          );

          // Create a schedule item
          const scheduleItem: ScheduleItem = {
            type: 'class',
            thread_id: thread.id,
            thread_name: thread.title,
            course_name: thread.course.title,
            schedule_id: schedule.id,
            start_time: startDate.toISOString(),
            end_time: endDate.toISOString(),
            location: schedule.location || 'SCHEDULE.classroom', // Use actual location if available
            booked_slots: thread.booked_slots,
            max_students: thread.max_students
          };

          schedules.push(scheduleItem);
        });
      }
    });

    return { schedules };
  }

  /**
   * Organize schedule items by day of the week
   */
  organizeScheduleByDay(): void {
    // Initialize empty week schedule
    this.weekSchedule = [];

    // Create a day entry for each day of the week
    for (let i = 0; i < 7; i++) {
      const date = new Date(this.startDate);
      date.setDate(this.startDate.getDate() + i);

      // Get the day name translation key
      const dayKey = this.dayNames[date.getDay()];

      this.weekSchedule.push({
        day: dayKey,
        date: new Date(date),
        items: []
      });
    }

    // If no schedule data, return
    if (!this.scheduleData || !this.scheduleData.schedules || !this.scheduleData.schedules.length) {
      return;
    }

    // Add schedule items to the appropriate day
    this.scheduleData.schedules.forEach((item: ScheduleItem) => {
      const startTime = new Date(item.start_time);
      const dayIndex = startTime.getDay();

      // Adjust for Sunday (0) to be the last day
      const adjustedIndex = dayIndex === 0 ? 6 : dayIndex - 1;

      // Add item to the appropriate day
      this.weekSchedule[adjustedIndex].items.push(item);
    });

    // Sort items by start time for each day
    this.weekSchedule.forEach(day => {
      day.items.sort((a, b) => {
        return new Date(a.start_time).getTime() - new Date(b.start_time).getTime();
      });
    });
  }

  /**
   * Format time from ISO string to HH:MM format
   */
  formatTime(isoString: string): string {
    const date = new Date(isoString);
    return date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
  }

  /**
   * Navigate to previous week
   */
  previousWeek(): void {
    this.startDate.setDate(this.startDate.getDate() - 7);
    this.endDate.setDate(this.endDate.getDate() - 7);
    this.fetchSchedule();
  }

  /**
   * Navigate to next week
   */
  nextWeek(): void {
    this.startDate.setDate(this.startDate.getDate() + 7);
    this.endDate.setDate(this.endDate.getDate() + 7);
    this.fetchSchedule();
  }

  /**
   * Get a readable location name
   * This is a helper method to display a default location if none is provided
   */
  getLocationName(item: ScheduleItem): string {
    if (item.location && item.location.trim() !== '' && item.location !== 'No location specified') {
      // If the location starts with 'SCHEDULE.', it's a translation key
      if (item.location.startsWith('SCHEDULE.')) {
        return this.translateService.instant(item.location);
      }
      // Just display the location as is
      return item.location;
    }
    // If no location or "No location specified", show "not assigned"
    return this.translateService.instant('SCHEDULE.not_assigned');
  }
}
