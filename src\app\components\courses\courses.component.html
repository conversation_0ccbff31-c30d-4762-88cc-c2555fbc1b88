<app-breadcrumbs [items]="breadcrumbs"/>
<div class="font-medium">{{ 'COURSES.your_courses_this_semester' | translate }}</div>
<div class="max-w-screen-lg mt-2">
  <div class="grid lg:grid-cols-2 grid-cols-1 text-xs gap-2">
    <!-- Search Input with Clear Button -->
    <div class="w-full rounded-xl border border-gray-300 dark:border-gray-600 flex items-center p-1 relative">
      <div class="px-1 h-full flex items-center">
        <img src="/assets/icons/search.svg" class="opacity-75 size-6 dark:invert" alt="Search">
      </div>
      <input
        type="text"
        [(ngModel)]="searchQuery"
        (input)="onSearchInput($event)"
        placeholder="{{ 'COURSES.search_placeholder' | translate }}"
        class="p-1 w-full    outline-0 dark:bg-transparent"
      >
      <!-- Clear button - only visible when there's text -->
      <button
        *ngIf="isSearching"
        (click)="clearSearch()"
        class="absolute right-2 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- View Toggle
    <div class="w-full flex justify-end">
      <app-dropdown
        [items]="menuItems"
        [buttonLabel]="buttonLabel"
        (selectionChanged)="changeView($event)"
      >
      </app-dropdown>
    </div> -->
  </div>

  <!-- Search Status Message -->
  <div *ngIf="isSearching" class="mt-2 text-xs text-gray-500 flex items-center">
    <span>{{ 'COURSES.search_for_query' | translate }}: "{{ searchQuery }}"</span>
    <button
      (click)="clearSearch()"
      class="ml-2 text-blue-500 hover:underline"
    >
      {{ 'COURSES.reset' | translate }}
    </button>
  </div>

  <!-- Courses List -->
  <div class="mt-3">
    <app-courses-list [gridClasses]="'grid 2xl:grid-cols-4 lg:grid-cols-3 md:grid-cols-2 grid-col-1 gap-4'">
    </app-courses-list>
  </div>
</div>

