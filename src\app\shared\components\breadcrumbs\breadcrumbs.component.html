<nav class="flex mt-1 mb-2" aria-label="Breadcrumb">
  <ol class="inline-flex flex-nowrap items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
    <ng-container *ngFor="let item of items; let i = index">
      <li class="inline-flex items-center" *ngIf="i === 0">
        <a *ngIf="item.url; else current0"
           [routerLink]="item.url"
           class="inline-flex  items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
          {{ item.label | translate }}
        </a>
        <ng-template #current0>
          <span class="text-sm font-medium text-gray-500 dark:text-gray-400 text-nowrap">{{ item.label | translate }}</span>
        </ng-template>
      </li>

      <li *ngIf="i > 0">
        <div class="flex items-center">
          <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" xmlns="http://www.w3.org/2000/svg" fill="none"
               viewBox="0 0 6 10" aria-hidden="true">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                  stroke-width="2" d="m1 9 4-4-4-4"/>
          </svg>
          <ng-container *ngIf="item.url; else current">
            <a [routerLink]="item.url"
               class="ms-1 text-sm text-nowrap font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">
              {{ item.label | translate }}
            </a>
          </ng-container>
          <ng-template #current>
            <span class="ms-1 text-nowrap text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">
              {{ item.label | translate }}
            </span>
          </ng-template>
        </div>
      </li>
    </ng-container>
  </ol>
</nav>
<hr class="mb-1.5">
