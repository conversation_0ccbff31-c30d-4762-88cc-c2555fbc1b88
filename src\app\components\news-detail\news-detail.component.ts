import {Component, OnInit} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import {MOCK_DATA} from "../../shared/mock-data";

@Component({
  selector: 'app-news-detail',
  templateUrl: './news-detail.component.html',
  styleUrl: './news-detail.component.css'
})
export class NewsDetailComponent implements OnInit {
  newsId: string | null = null;
  news: any = null;

  breadcrumbs: { label: string, url?: string }[] = [];

  constructor(private route: ActivatedRoute) {}

  ngOnInit() {
    this.newsId = this.route.snapshot.paramMap.get('id');
    const latestNews = MOCK_DATA.latestNews;

    // Находим новость по id
    this.news = latestNews.find((item) => item.id === Number(this.newsId));

    this.breadcrumbs = [
      { label: 'Главная', url: '/' },
      { label: 'Новости', url: '/news' },
      { label: this.news.title || 'Новость',  },
    ]
  }


}
