<!-- Заголовок -->
<div class="text-xl  text-gray-800 mt-5 px-5">Добавить Assignment groups</div>

<!-- Форма -->
<form [formGroup]="assignmentGroupForm" class="space-y-3  p-5 px-8">

  <!-- Тип -->
  <div>
    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Тип</label>
    <select
      id="type"
      formControlName="group_type"
      class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring focus:ring-blue-200"
    >
      <option value="midterm">Midterm</option>
      <option value="endterm">Endterm</option>
      <option value="final">Final</option>
      <option value="custom">Custom</option>
    </select>
  </div>

  <!-- Заголовок -->
  <div>
    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Название группы</label>
    <input
      type="text"
      id="title"
      formControlName="name"
      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200"
    />
  </div>

  <!-- Описание -->
  <div>
    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Вес</label>
    <input
      type="number"
      id="description"
      formControlName="weight"
      class="w-full border border-gray-300 rounded-md px-3 py-2 resize-none focus:outline-none focus:ring focus:ring-blue-200"
    />
    <div *ngIf="assignmentGroupForm.get('weight')?.errors?.['max']" class="text-red-500 text-sm mt-1">
      Вес не должен превышать 1.0
    </div>
    <div *ngIf="assignmentGroupForm.get('weight')?.errors?.['min']" class="text-red-500 text-sm mt-1">
      Вес должен быть больше 0.0
    </div>
  </div>


  <!-- Действия -->
  <div class="flex justify-end space-x-2 pt-4">
    <button
      type="button"
      (click)="cancel()"
      class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition"
    >
      Отмена
    </button>
    <button
      type="button"
      (click)="submit()"
      [disabled]="assignmentGroupForm.invalid"
      class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition disabled:opacity-50 disabled:cursor-not-allowed"
    >
      Добавить
    </button>
  </div>
</form>
