/* Progress Circle Styles */
.progress-circle {
  --size: 90px;
  --value: 0%;
  --thickness: 8px;
  --bg: #e5e7eb;
  --color: #3b82f6;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  background: conic-gradient(var(--color) var(--value), var(--bg) 0);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: background 0.5s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.progress-circle::before {
  content: '';
  position: absolute;
  width: calc(var(--size) - var(--thickness) * 2);
  height: calc(var(--size) - var(--thickness) * 2);
  background-color: white;
  border-radius: 50%;
  z-index: 1;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.progress-text {
  position: relative;
  font-size: 1.1rem;
  font-weight: 500;
  z-index: 2;
  color: #111827;
}

/* Sidebar Styles */
.rounded-lg {
  transition: all 0.3s ease;
}

.rounded-lg:hover {
  transform: translateY(-2px);
}

/* Menu Item Styles */
ul li {
  transition: all 0.2s ease;
}

ul li:hover {
  transform: translateX(2px);
}

ul li.active {
  position: relative;
}

ul li.active::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 70%;
  background-color: #3b82f6;
  border-radius: 3px 0 0 3px;
}

/* Button Styles */
button {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(1px);
}

button::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

button:hover::after {
  transform: translateX(0);
}

/* Icon Styles */
.rounded-full {
  transition: all 0.2s ease;
}

.rounded-full:hover {
  transform: scale(1.05);
}

/* Section Header Styles */
.text-red-700, .text-blue-700, .text-green-700 {
  position: relative;
  display: inline-block;
}

.text-red-700::after, .text-blue-700::after, .text-green-700::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  transition: width 0.3s ease;
}

.text-red-700::after {
  background-color: #ef4444;
}

.text-blue-700::after {
  background-color: #3b82f6;
}

.text-green-700::after {
  background-color: #10b981;
}

.text-red-700:hover::after, .text-blue-700:hover::after, .text-green-700:hover::after {
  width: 100%;
}

/* Attendance Display Styles */
.flex.items-center.text-xl {
  transition: all 0.2s ease;
}

.flex.items-center.text-xl:hover {
  transform: scale(1.05);
}

/* Active Tab Indicator Animation */
@keyframes slideIn {
  0% { opacity: 0; transform: translateX(-10px); }
  100% { opacity: 1; transform: translateX(0); }
}

.ml-auto svg {
  animation: slideIn 0.3s ease forwards;
}

/* Mobile Sidebar Toggle Button */
button[class*="bg-blue-50"] {
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

button[class*="bg-blue-50"]:hover {
  transform: translateY(-1px);
}

button[class*="bg-blue-50"]:active {
  transform: translateY(1px);
}

/* Mobile Sidebar Animation */
@keyframes slideDown {
  0% { opacity: 0; transform: translateY(-10px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes slideUp {
  0% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-10px); }
}

.sidebar-visible {
  animation: slideDown 0.3s ease forwards;
}

.sidebar-hidden {
  animation: slideUp 0.3s ease forwards;
}

/* Responsive Adjustments */
@media (max-width: 1023px) {
  .progress-circle {
    --size: 80px;
  }

  .progress-text {
    font-size: 1rem;
  }
}
