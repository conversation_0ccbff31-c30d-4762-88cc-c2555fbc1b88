@import './app/styles/theme.css';
@import './app/styles/fonts.css';
@import './app/styles/cyrillic-fix.css';
@import './app/styles/material-overrides.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

html, body {
  height: 100%;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

body {
  margin: 0;
  font-family: 'SF Pro', 'SF Pro Cyrillic Fallback', system-ui, Avenir, Helvetica, Arial, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s, color 0.3s;
  position: relative;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern", "liga", "calt";
  letter-spacing: -0.01em;
}

/* Text size utilities */
.text-15 {
  font-size: 15px !important;
  line-height: 1.5 !important;
}

.text-13 {
  font-size: 13px !important;
  line-height: 1.615 !important;
}

.text-10 {
  font-size: 10px !important;
  line-height: 1.6 !important;
}

/* Card component */
@layer components {
  .card {
    @apply bg-card rounded-lg border shadow-sm p-4;
  }
}

/* Button styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg transition-colors duration-200 focus:outline-none;
  }

  .btn-primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }

  .btn-secondary {
    @apply bg-tertiary text-primary hover:bg-gray-200;
  }
}

/* Form controls */
@layer components {
  .form-input {
    @apply w-full px-3 py-2 bg-input border border-input rounded-lg focus:outline-none focus:border-input-focus;
  }
}

/* Layout utilities */
@layer components {
  .sidebar {
    @apply fixed left-0 top-0 h-screen border-r transition-all duration-300 z-10;
  }

  .header {
    @apply h-16 flex items-center justify-between px-6 sticky top-0 z-30 border-b;
  }

  .main-content {
    @apply p-6 overflow-auto;
  }
}

/* Calendar styles */
.calendar-small {
  font-size: 0.75rem !important;
  --mat-datepicker-calendar-date-size: 28px;
}

.calendar-small .mat-calendar-body-cell-content {
  width: 24px !important;
  height: 24px !important;
  line-height: 24px !important;
}

.calendar-small .mat-calendar-table-header th {
  padding: 0 0 4px 0 !important;
}

.calendar-small .mat-calendar-body-label {
  padding: 4px 0 !important;
}

/* Fix calendar text color in dark mode */
.dark .mat-calendar-body-cell-content {
  color: var(--text-primary) !important;
}

.dark .mat-calendar-table-header,
.dark .mat-calendar-body-label,
.dark .mat-calendar-period-button,
.dark .mat-calendar-arrow,
.dark .mat-calendar-previous-button,
.dark .mat-calendar-next-button {
  color: var(--text-primary) !important;
}

/* Make tasks section smaller */
.card .max-h-80 {
  max-height: 200px !important;
}

