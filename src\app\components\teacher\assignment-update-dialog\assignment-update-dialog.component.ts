import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { WeekService } from "../../../core/services/week.service";
import { AssignmentService } from "../../../core/services/assignment.service";

import { EditorComponent } from "@tinymce/tinymce-angular";
import {environments} from "../../../environments/environments";

@Component({
  selector: 'app-assignment-update-dialog',
  templateUrl: './assignment-update-dialog.component.html',
  styleUrl: './assignment-update-dialog.component.css'
})
export class AssignmentUpdateDialogComponent implements OnInit {
  assignmentForm!: FormGroup;
  assignmentGroups: any[] = [];
  weeks: any[] = [];
  editor_key = environments.TINY_MC_KEY;
  init: EditorComponent['init'] = {
    plugins: 'lists link image table code wordcount'
  };
  minDate: string = '';

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AssignmentUpdateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { assignment: any, threadId: number },
    private weekService: WeekService,
    private assignmentService: AssignmentService
  ) {}

  ngOnInit(): void {
    // Set minimum date to today
    const today = new Date();
    today.setMinutes(today.getMinutes() - today.getTimezoneOffset());
    this.minDate = today.toISOString().slice(0, 16);

    // Check if the assignment type is 'info'
    const isInfoType = this.data.assignment.type === 'info';

    // For info type, use a default future date if none exists
    let dueDate = this.formatDate(this.data.assignment.due_date);
    if (isInfoType && (!dueDate || dueDate === '')) {
      const defaultFutureDate = new Date('2027-07-07T07:07:00');
      defaultFutureDate.setMinutes(defaultFutureDate.getMinutes() - defaultFutureDate.getTimezoneOffset());
      dueDate = defaultFutureDate.toISOString().slice(0, 16);
    }

    this.assignmentForm = this.fb.group({
      week_id: [this.data.assignment.week_id, Validators.required],
      title: [this.data.assignment.title, [Validators.required, Validators.minLength(5)]],
      description: [this.data.assignment.description],
      due_date: [{
        value: dueDate,
        disabled: isInfoType
      }, isInfoType ? null : Validators.required],
      max_points: [{
        value: this.data.assignment.max_points || 0,
        disabled: isInfoType
      }, isInfoType ? null : [Validators.required, Validators.min(0), Validators.max(100)]],
      assignment_group_id: [this.data.assignment.assignment_group_id, Validators.required],
      type: [this.data.assignment.type, Validators.required]
    });

    this.loadWeeks();
    this.loadAssignmentGroups();

    // Listen for changes to the type field
    this.assignmentForm.get('type')?.valueChanges.subscribe(type => {
      this.handleTypeChange(type);
    });
  }

  /**
   * Handle changes to the assignment type
   */
  handleTypeChange(type: string): void {
    const maxPointsControl = this.assignmentForm.get('max_points');
    const dueDateControl = this.assignmentForm.get('due_date');

    if (type === 'info') {
      // For info type, set max_points to null and disable
      maxPointsControl?.setValue(null);
      maxPointsControl?.disable();

      // Set a default far future date (July 7, 2027 07:07) and disable the field
      const defaultFutureDate = new Date('2027-07-07T07:07:00');
      defaultFutureDate.setMinutes(defaultFutureDate.getMinutes() - defaultFutureDate.getTimezoneOffset());
      dueDateControl?.setValue(defaultFutureDate.toISOString().slice(0, 16));
      dueDateControl?.disable();
    } else {
      // For task type, enable controls
      maxPointsControl?.enable();
      dueDateControl?.enable();
    }
  }

  private formatDate(dueDate: any): string {
    if (!dueDate) return '';

    // Handle Firestore timestamp format
    if (dueDate.seconds) {
      const date = new Date(dueDate.seconds * 1000);
      return date.toISOString().substring(0, 16);
    }

    // Handle ISO string format
    if (typeof dueDate === 'string') {
      return new Date(dueDate).toISOString().substring(0, 16);
    }

    return '';
  }

  loadWeeks(): void {
    if (this.data.threadId) {
      this.weekService.getAllWeeksByThread(this.data.threadId).subscribe({
        next: (response: any) => {
          this.weeks = response;
        },
        error: (err) => {
          console.error('Error loading weeks:', err);
        }
      });
    }
  }

  loadAssignmentGroups(): void {
    if (this.data.threadId) {
      this.assignmentService.getListAssignmentGroupForThread(this.data.threadId).subscribe({
        next: (response: any) => {
          this.assignmentGroups = response;
        },
        error: (err) => {
          console.error('Error loading assignment groups:', err);
        }
      });
    }
  }

  submit(): void {
    if (this.assignmentForm.invalid) return;

    // Get both enabled and disabled controls
    const formValues: Record<string, any> = {
      ...this.assignmentForm.value,
      ...Object.keys(this.assignmentForm.controls)
        .filter(key => this.assignmentForm.controls[key].disabled)
        .reduce((acc: Record<string, any>, key: string) => {
          acc[key] = this.assignmentForm.controls[key].value;
          return acc;
        }, {})
    };

    // For info type, use the default future date
    let isoDate = '';
    if (formValues['type'] === 'info') {
      const defaultFutureDate = new Date('2027-07-07T07:07:00');
      isoDate = defaultFutureDate.toISOString();
    } else if (formValues['due_date']) {
      isoDate = new Date(formValues['due_date']).toISOString();
    }

    // For info type, don't include max_points
    const points = formValues['type'] === 'info' ? null : formValues['max_points'];

    this.dialogRef.close({
      ...formValues,
      due_date: isoDate,
      max_points: points
    });
  }

  cancel(): void {
    this.dialogRef.close();
  }
}
