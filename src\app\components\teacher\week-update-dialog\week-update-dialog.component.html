<!-- Заголовок -->
<div class="text-xl text-gray-800 mt-5 px-5">Update Week</div>

<!-- Форма -->
<form [formGroup]="weekForm" class="space-y-3 p-5 px-8">
  <!-- Номер недели -->
  <div>
    <label for="week_number" class="block text-sm font-medium text-gray-700 mb-1">Week Number</label>
    <input
      type="number"
      id="week_number"
      formControlName="week_number"
      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200"
    />
    <div *ngIf="weekForm.get('week_number')?.invalid && weekForm.get('week_number')?.touched" 
         class="text-xs text-red-500 mt-1">
      Week number is required and must be at least 1
    </div>
  </div>

  <!-- Тип -->
  <div>
    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
    <select
      id="type"
      formControlName="type"
      class="w-full border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring focus:ring-blue-200"
    >
      <option value="midterm">Midterm</option>
      <option value="endterm">Endterm</option>
      <option value="final">Final</option>
      <option value="custom">Custom</option>
    </select>
    <div *ngIf="weekForm.get('type')?.invalid && weekForm.get('type')?.touched" 
         class="text-xs text-red-500 mt-1">
      Type is required
    </div>
  </div>

  <!-- Заголовок -->
  <div>
    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
    <input
      type="text"
      id="title"
      formControlName="title"
      class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring focus:ring-blue-200"
    />
    <div *ngIf="weekForm.get('title')?.invalid && weekForm.get('title')?.touched" 
         class="text-xs text-red-500 mt-1">
      Title is required
    </div>
  </div>

  <!-- Описание -->
  <div>
    <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
    <textarea
      id="description"
      formControlName="description"
      rows="4"
      class="w-full border border-gray-300 rounded-md px-3 py-2 resize-none focus:outline-none focus:ring focus:ring-blue-200"
    ></textarea>
  </div>

  <!-- Действия -->
  <div class="flex justify-end space-x-2 pt-4">
    <button
      type="button"
      (click)="cancel()"
      class="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded-md transition"
    >
      Cancel
    </button>
    <button
      type="button"
      (click)="submit()"
      [disabled]="weekForm.invalid"
      class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition disabled:opacity-50 disabled:cursor-not-allowed"
    >
      Update
    </button>
  </div>
</form>
