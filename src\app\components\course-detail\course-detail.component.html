<app-breadcrumbs [items]="breadcrumbs" />
<div *ngIf="courseInfo" class="max-w-screen-2xl">
  <div class="text-2xl xl:w-[70%]">
    <div class="text-blue-800 font-medium font-gotham">
      {{ courseInfo.title }}
    </div>
  </div>

  <div class="my-1 mb-3">
    <div class="text-sm">
      {{ courseInfo.description }}
    </div>
    <div class="mt-1 text-xs text-gray-800">
      <span class="text-blue-500">Thread №:</span>
      <span class="font-medium">{{ threadInfo.title }}</span>
    </div>
  </div>

  <!-- Mobile sidebar toggle button -->
  <div class="lg:hidden flex justify-end mb-2">
    <button
      (click)="toggleSidebar()"
      class="flex items-center justify-center p-2 rounded-md bg-blue-50 text-blue-700 hover:bg-blue-100 transition-all duration-200"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M4 6h16M4 12h16M4 18h7"
        />
      </svg>
      <span class="ml-2 text-sm font-medium">{{
        isSidebarCollapsed ? "Показать меню" : "Скрыть меню"
      }}</span>
    </button>
  </div>

  <div class="flex w-full flex-wrap-reverse mt-1 justify-between">
    <div *ngIf="activeTab === 'general'" class="lg:w-[70%] w-full">
      <app-course-weeks [threadID]="threadID" [user]="user"></app-course-weeks>
    </div>

    <div *ngIf="activeTab === 'assignments'" class="lg:w-[70%] w-full">
      <app-assignments></app-assignments>
    </div>

    <div *ngIf="activeTab === 'program'" class="lg:w-[70%] w-full">
      <app-study-program></app-study-program>
    </div>

    <div *ngIf="activeTab === 'grades'" class="lg:w-[70%] w-full">
      <app-course-grades></app-course-grades>
    </div>

    <div *ngIf="activeTab === 'people'" class="lg:w-[70%] w-full">
      <app-course-people [threadID]="threadID"></app-course-people>
    </div>

    <div *ngIf="activeTab === 'set-rate'" class="lg:w-[77%] w-full">
      <app-set-rating></app-set-rating>
    </div>

    <div *ngIf="activeTab === 'set-attendance'" class="lg:w-[77%] w-full">
      <app-set-attendance [threadID]="threadID"></app-set-attendance>
    </div>

    <div *ngIf="activeTab === 'add-task'" class="lg:w-[77%] w-full">
      <app-add-task [threadID]="threadID"></app-add-task>
    </div>

    <div *ngIf="activeTab === 'teacher-panel'" class="lg:w-[77%] w-full">
      <app-teacher-management-panel
        [threadID]="threadID"
        [userID]="user.id"
      ></app-teacher-management-panel>
    </div>

    <div
      class="lg:w-[20%] w-full"
      [ngClass]="{
        hidden: isSidebarCollapsed && !isLargeScreen,
        'sidebar-visible': !isSidebarCollapsed && !isLargeScreen,
        'sidebar-hidden': isSidebarCollapsed && !isLargeScreen
      }"
      #sidebarContainer
    >
      <!-- Main sidebar panel -->
      <div
        class="rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-5 w-full bg-white dark:bg-gray-800 z-50 transition-all duration-300 hover:shadow-md"
      >
        <!-- Course navigation section -->
        <div class="flex items-center gap-2 mb-3">
          <div class="text-red-700 dark:text-red-400 font-medium">Разделы</div>
        </div>

        <!-- Menu sections -->
        <ul
          class="text-black dark:text-gray-200 text-xs mt-1 border-b border-gray-100 dark:border-gray-700 pb-4 space-y-1"
        >
          <li
            *ngFor="let section of menuSections"
            class="flex items-center px-3 py-2 gap-2 cursor-pointer rounded-md transition-all duration-200"
            [ngClass]="{
              'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 shadow-sm': activeTab === section.key,
              'hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-blue-500 dark:hover:text-blue-400': activeTab !== section.key
            }"
            (click)="setActiveTab(section.key)"
          >
            <div
              class="flex items-center justify-center w-6 h-6 rounded-full"
              [ngClass]="{
                'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300': activeTab === section.key,
                'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': activeTab !== section.key
              }"
            >
              <div class="flex items-center justify-center w-full h-full">
                <img
                  [src]="section.icon"
                  class="size-3.5 logo dark:invert"
                  width="48"
                  height="48"
                  alt=""
                />
              </div>
            </div>
            <span class="font-medium">{{ section.title | translate }}</span>

            <!-- Active indicator -->
            <div *ngIf="activeTab === section.key" class="ml-auto">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 text-blue-500 dark:text-blue-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </div>
          </li>
        </ul>

        <!-- Student statistics section -->
        <div *ngIf="user.role === 'student'" class="mt-4 pt-2">
          <div class="flex items-center gap-2 mb-3">
            <div class="text-green-700 dark:text-green-500 font-medium">Ваша статистика</div>
          </div>

          <!-- Overall grade with loading state -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3">
            <div class="text-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Общая оценка:
            </div>

            <!-- Loading indicator -->
            <div *ngIf="!assignmentsLoaded" class="flex justify-center items-center py-2">
              <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
            </div>

            <!-- Grade display -->
            <div *ngIf="assignmentsLoaded" class="flex flex-col items-center">
              <div class="progress-circle mb-2" [ngStyle]="progressStyle">
                <div class="progress-text dark:text-white">{{ value }}%</div>
              </div>

              <!-- Points summary -->
              <div class="text-xs text-gray-600 dark:text-gray-300 mt-2 text-center">
                <div>Набрано баллов: <span class="font-medium">{{ earnedPoints }}</span></div>
                <div>Всего баллов: <span class="font-medium">{{ totalPoints }}</span></div>
              </div>

              <!-- Letter grade -->
              <div *ngIf="totalPoints > 0" class="mt-2 px-3 py-1 rounded-full text-xs font-medium"
                   [ngClass]="{
                     'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200': finalValue >= 80,
                     'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200': finalValue >= 50 && finalValue < 80,
                     'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200': finalValue < 50
                   }">
                {{ finalValue >= 90 ? 'A' :
                   finalValue >= 80 ? 'B' :
                   finalValue >= 70 ? 'C' :
                   finalValue >= 60 ? 'D' : 'F' }}
              </div>
            </div>

            <!-- No assignments message -->
            <div *ngIf="assignmentsLoaded && totalPoints === 0" class="text-xs text-gray-500 dark:text-gray-400 text-center mt-2">
              Нет оцениваемых заданий в этом курсе
            </div>
          </div>
        </div>

        <!-- Management section - only for teachers and admins -->
        <div
          *ngIf="user.role === 'teacher' || user.role === 'admin'"
          class="mt-4 pt-2"
        >
          <div class="flex items-center gap-2 mb-3">
            <div class="text-blue-700 dark:text-blue-400 font-medium">Управление потоком</div>
          </div>

          <div class="text-xs text-gray-600 dark:text-gray-300 mb-3 bg-blue-50 dark:bg-blue-900/30 p-3 rounded-md">
            Используйте панель управления для управления неделями, группами
            заданий и заданиями.
          </div>

          <div class="space-y-2">
            <button
              class="flex items-center justify-center gap-2 w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-md text-xs transition-all duration-200 shadow-sm hover:shadow"
              (click)="setActiveTab('teacher-panel')"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                />
              </svg>
              <span>Панель управления</span>
            </button>

            <button
              class="flex items-center justify-center gap-2 w-full px-4 py-2 bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white rounded-md text-xs transition-all duration-200 shadow-sm hover:shadow"
              (click)="setActiveTab('add-task')"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <span>Добавить задание</span>
            </button>

            <button
              class="flex items-center justify-center gap-2 w-full px-4 py-2 bg-purple-500 hover:bg-purple-600 dark:bg-purple-600 dark:hover:bg-purple-700 text-white rounded-md text-xs transition-all duration-200 shadow-sm hover:shadow"
              (click)="setActiveTab('set-attendance')"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span>Отметить посещаемость</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Additional content can be added here -->
      <div class="mt-4">
        <!-- Course quick stats or other information can go here -->
      </div>
    </div>
  </div>
</div>
<!-- Loading state -->
<div
  *ngIf="!courseInfo"
  class="w-full flex flex-col items-center justify-center py-12"
>
  <div
    class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400 mb-4"
  ></div>
  <div class="text-base font-medium text-gray-700 dark:text-gray-300">
    Загрузка информации о курсе...
  </div>
</div>
