<div class="max-w-screen-xl min-h-screen">
  <div class="font-medium text-base">
    Создать задание
  </div>
  <div class="text-sm max-w-5xl mt-3">
    <form [formGroup]="assignmentForm" (ngSubmit)="createAssignment()">
      <div class="mb-5 grid grid-cols-3 gap-5">
        <!-- Группа заданий -->
        <div>
          <label class="block text-sm font-medium text-gray-500">
            1. Выберите группу заданий:
          </label>
          <div class="relative">
            <select formControlName="assignment_group_id"
                    class="block w-full appearance-none rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600">
              <option value="" disabled selected>Выберите группу</option>
              <option *ngFor="let group of assignmentGroups" [value]="group.id">
                {{ group.name }}
              </option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
              <!-- Стрелка -->
            </div>
          </div>
          <div *ngIf="assignmentForm.get('assignment_group_id')?.invalid && assignmentForm.get('assignment_group_id')?.touched"
               class="text-xs text-red-500 mt-1">
            Пожалуйста, выберите группу заданий.
          </div>
        </div>


        <div>
          <label class="block text-sm font-medium text-gray-500">
            2. Выберите Неделю :
          </label>
          <div class="relative">
            <select formControlName="week_id"
                    class="block w-full appearance-none rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600">
              <option value="" disabled selected>Выберите неделю</option>
              <option *ngFor="let week of weeks" [value]="week.id">
                Title: {{ week.title }} type: {{week.type}}
              </option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
              <!-- Стрелка -->
            </div>
          </div>
          <div *ngIf="assignmentForm.get('assignment_group_id')?.invalid && assignmentForm.get('assignment_group_id')?.touched"
               class="text-xs text-red-500 mt-1">
            Пожалуйста, выберите группу заданий.
          </div>
        </div>


        <!-- Тема -->
        <div>
          <label class="block text-sm font-medium text-gray-500">3. Введите тему задания:</label>
          <input type="text" formControlName="title"
                 class="border text-sm rounded-lg p-2 w-full outline-0 focus:border-blue-600 focus:ring-1 focus:ring-blue-600"
                 placeholder="Тема">
          <div *ngIf="assignmentForm.get('title')?.invalid && assignmentForm.get('title')?.touched"
               class="text-xs text-red-500 mt-1">
            Пожалуйста, введите тему задания.
          </div>
        </div>
      </div>

      <!-- Тема и дедлайн -->
      <div class="mb-5 grid grid-cols-3 gap-5">

        <!-- Группа заданий -->
        <div>
          <label class="block text-sm font-medium text-gray-500">
            4. Тип (Лекция / Задание):
          </label>
          <div class="relative">
            <select formControlName="type"
                    class="block w-full appearance-none rounded-lg border bg-white p-2 text-sm transition focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600">
              <option value="info">
                Лекция
              </option>
              <option value="task">
                Задание
              </option>
            </select>
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
              <!-- Стрелка -->
            </div>
          </div>
          <div *ngIf="assignmentForm.get('type')?.invalid && assignmentForm.get('type')?.touched"
               class="text-xs text-red-500 mt-1">
            Пожалуйста, выберите TYPE.
          </div>
        </div>

        <!-- Баллы - только для заданий типа "task" -->
        <div>
          <div class="block text-sm font-medium text-gray-500">
            5. Количество баллов за задание:
          </div>
          <!-- Show input only for task type -->
          <ng-container *ngIf="assignmentForm.get('type')?.value === 'task'">
            <input type="number" formControlName="max_points"
                   class="border text-sm rounded-lg p-2 w-full outline-0 focus:border-blue-600 focus:outline-none focus:ring-1 focus:ring-blue-600"
                   placeholder="100">
            <div *ngIf="assignmentForm.get('max_points')?.invalid && assignmentForm.get('max_points')?.touched"
                 class="text-xs text-red-500 mt-1">
              <div *ngIf="assignmentForm.get('max_points')?.errors?.['required']">Это поле обязательно.</div>
              <div *ngIf="assignmentForm.get('max_points')?.errors?.['min']">Минимум — 0.</div>
              <div *ngIf="assignmentForm.get('max_points')?.errors?.['max']">Максимум — 100.</div>
            </div>
          </ng-container>
          <!-- Show message for info type -->
          <div *ngIf="assignmentForm.get('type')?.value === 'info'" class="text-sm text-gray-500 mt-2 p-2 border border-gray-200 rounded-lg bg-gray-50">
            Баллы не требуются для лекций.
          </div>
        </div>

        <!-- Дедлайн - только для заданий типа "task" -->
        <div>
          <label class="block text-sm font-medium text-gray-500">6. Дата окончания (Deadline):</label>
          <!-- Show input only for task type -->
          <ng-container *ngIf="assignmentForm.get('type')?.value === 'task'">
            <input type="datetime-local" formControlName="due_date"
                   [min]="minDate"
                   class="border text-sm rounded-lg p-2 w-full outline-0 focus:border-blue-600 focus:ring-1 focus:ring-blue-600">
            <div *ngIf="assignmentForm.get('due_date')?.invalid && assignmentForm.get('due_date')?.touched"
                 class="text-xs text-red-500 mt-1">
              Укажите дату и время завершения.
            </div>
          </ng-container>
          <!-- Show message for info type -->
          <div *ngIf="assignmentForm.get('type')?.value === 'info'" class="text-sm text-gray-500 mt-2 p-2 border border-gray-200 rounded-lg bg-gray-50">
            Дедлайн не требуется для лекций.
          </div>
        </div>
      </div>

      <!-- Описание -->
      <div class="mb-5">
        <label class="block text-sm font-medium text-gray-500">7. Описание задачи:</label>
        <editor [apiKey]="editor_key" [init]="init" formControlName="description"></editor>
        <div *ngIf="assignmentForm.get('description')?.invalid && assignmentForm.get('description')?.touched"
             class="text-xs text-red-500 mt-1">
          Пожалуйста, добавьте описание задания.
        </div>
      </div>

      <!-- File Upload -->
      <div class="mb-5">
        <label class="block text-sm font-medium text-gray-500">8. Прикрепить файл (опционально):</label>
        <div class="mt-2">
          <label class="flex items-center justify-center px-4 py-2 border border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <span class="text-sm text-gray-500">Выберите файл</span>
            <input type="file" class="hidden" (change)="onFileSelected($event)">
          </label>
        </div>

        <!-- Selected File Display -->
        <div *ngIf="selectedFile" class="mt-2 p-2 border border-gray-200 rounded-lg bg-gray-50 flex justify-between items-center">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span class="text-sm text-gray-700">{{ selectedFile.name }}</span>
          </div>
          <button type="button" (click)="removeFile()" class="text-red-500 hover:text-red-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <div>
        <button type="submit"
                class="border-blue-500 text-xs text-blue-700 font-medium border p-2 rounded-lg hover:shadow-md">
          Добавить
        </button>
      </div>
    </form>

  </div>
</div>
