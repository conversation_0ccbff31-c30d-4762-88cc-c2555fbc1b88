import { Injectable } from '@angular/core';
import {Observable, tap} from "rxjs";
import {HttpClient} from "@angular/common/http";
import {Router} from "@angular/router";
import {StorageService} from "./storage.service";
import {environments} from "../../environments/environments";

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private apiUrl = environments.API

  constructor(
    private http: HttpClient,
    private router: Router,
    private storage: StorageService
  ) {}

  login(credentials: { username: string; password: string }) {
    return this.http.post<any>(`${this.apiUrl}/auth/login`, credentials).pipe(
      tap(response => {
        this.storage.saveToken(response.access_token);
        this.storage.saveRefreshToken(response.refresh_token);
        this.storage.saveUser(response.user);
      })
    );
  }

  logout() {
    this.storage.clear();
    this.router.navigate(['/login']);
  }

  isLoggedIn(): boolean {
    return !!this.storage.getToken();
  }

  getCurrentUser() {
    return this.storage.getUser();
  }

  refreshAccessToken(): Observable<any> {
    const refresh = this.storage.getRefreshToken();
    return this.http.post<any>(`${this.apiUrl}refresh/`, { refresh });
  }
}
