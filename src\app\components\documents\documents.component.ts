import { Component, OnInit } from '@angular/core';
import { DOCUMENTS_DATA } from '../../shared/mock-data';

@Component({
  selector: 'app-documents',
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.css']
})
export class DocumentsComponent implements OnInit {
  breadcrumbs: { label: string, url?: string }[] = [
    { label: 'Главная', url: '/' },
    { label: 'Документы' },
  ];

  documents = DOCUMENTS_DATA;
  filteredDocuments = [...DOCUMENTS_DATA];

  // Search functionality
  searchQuery: string = '';
  isSearching: boolean = false;

  // Filter state
  selectedCategory: string = 'all';
  categories = [
    { id: 'all', name: 'Все документы' },
    { id: 'academic', name: 'Учебные материалы' },
    { id: 'administrative', name: 'Административные' },
    { id: 'forms', name: 'Формы и заявления' },
    { id: 'guidelines', name: 'Руководства' }
  ];

  constructor() { }

  ngOnInit(): void {
    this.applyFilters();
  }

  onSearchInput(event: Event): void {
    const query = (event.target as HTMLInputElement).value;
    this.searchQuery = query;
    this.isSearching = query.length > 0;
    this.applyFilters();
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.isSearching = false;
    this.applyFilters();
  }

  selectCategory(categoryId: string): void {
    this.selectedCategory = categoryId;
    this.applyFilters();
  }

  applyFilters(): void {
    let result = [...DOCUMENTS_DATA];

    // Apply category filter
    if (this.selectedCategory !== 'all') {
      result = result.filter(doc => doc.category === this.selectedCategory);
    }

    // Apply search filter
    if (this.searchQuery) {
      const query = this.searchQuery.toLowerCase();
      result = result.filter(doc =>
        doc.name.toLowerCase().includes(query) ||
        doc.description.toLowerCase().includes(query)
      );
    }

    this.filteredDocuments = result;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  
}
