import {Component, EventEmitter, HostListener, Input, Output} from '@angular/core';
import {
  trigger,
  transition,
  style,
  animate,
} from '@angular/animations';

export interface DropdownItem {
  label: string;
  url?: string;
  type?: 'link' | 'button';
  icon?: string; // URL или CSS-класс иконки
}



@Component({
  selector: 'app-dropdown',
  templateUrl: './dropdown.component.html',
  styleUrl: './dropdown.component.css',
  animations: [
    trigger('menuAnimation', [
      transition(':enter', [
        style({ opacity: 0, transform: 'scale(0.95)' }),
        animate('100ms ease-out', style({ opacity: 1, transform: 'scale(1)' }))
      ]),
      transition(':leave', [
        animate('75ms ease-in', style({ opacity: 0, transform: 'scale(0.95)' }))
      ])
    ])
  ]
})

export class DropdownComponent {
  @Input() items: DropdownItem[] = []; // Массив объектов для меню
  @Input() buttonLabel: string = 'Options'; // Метка кнопки (можно переопределить)
  // Эмитируем выбранный пункт меню
  @Output() selectionChanged = new EventEmitter<DropdownItem>();


  isOpen = false;

  toggleDropdown() {
    this.isOpen = !this.isOpen;
  }

  onItemClick(item: DropdownItem) {
    this.selectionChanged.emit(item);
    this.isOpen = false;
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.dropdown-container')) {
      this.isOpen = false;
    }
  }
}
