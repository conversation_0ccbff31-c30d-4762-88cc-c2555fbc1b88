import {Component} from '@angular/core';
import {ThemeService} from "./core/services/theme.service";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'edunite_v2';

  constructor(private themeService: ThemeService,
              private translate: TranslateService) {
    // Применяем тему
    this.themeService.applyTheme();
    this.themeService.applyTheme();

    // Доступные языки
    const availableLangs = ['en', 'ru', 'kk'];
    this.translate.addLangs(availableLangs);
    this.translate.setDefaultLang('en');

    // Проверяем, есть ли язык в localStorage
    const savedLang = localStorage.getItem('language');
    const browserLang = this.translate.getBrowserLang();

    // Определяем, какой язык использовать
    const selectedLang = savedLang && availableLangs.includes(savedLang)
      ? savedLang
      : browserLang?.match(/en|ru|kk/) ? browserLang : 'en';

    this.translate.use(selectedLang);

    // Set the lang attribute on the HTML element
    document.documentElement.lang = selectedLang;

    // Subscribe to language changes to update the lang attribute
    this.translate.onLangChange.subscribe(event => {
      document.documentElement.lang = event.lang;
    });
  }

  switchLanguage(lang: string) {
    this.translate.use(lang);
    localStorage.setItem('language', lang); // Сохраняем выбор пользователя
  }


}
