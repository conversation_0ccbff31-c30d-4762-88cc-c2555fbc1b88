/* Cyrillic text fix */
[lang="ru"], [lang="kk"] {
  font-family: Arial, Helvetica, sans-serif;
  letter-spacing: normal;
  font-size: 0.98em;
  line-height: 1.5;
}

/* Apply SF Pro only to Latin characters in Cyrillic text */
[lang="ru"] *:not(:lang(ru)),
[lang="kk"] *:not(:lang(kk)) {
  font-family: 'SF Pro', 'SF Pro Display', system-ui, Avenir, Helvetica, Arial, sans-serif;
}

/* Fix for specific Cyrillic characters that might cause issues */
[lang="ru"] .text-xs,
[lang="ru"] .text-sm,
[lang="ru"] .text-base,
[lang="ru"] .text-lg,
[lang="ru"] .text-xl,
[lang="ru"] .text-2xl,
[lang="ru"] .text-3xl,
[lang="kk"] .text-xs,
[lang="kk"] .text-sm,
[lang="kk"] .text-base,
[lang="kk"] .text-lg,
[lang="kk"] .text-xl,
[lang="kk"] .text-2xl,
[lang="kk"] .text-3xl {
  letter-spacing: -0.01em;
  font-kerning: normal;
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  [lang="ru"], [lang="kk"] {
    font-size: 1em;
    letter-spacing: normal;
    font-family: Arial, Helvetica, sans-serif;
  }

  /* Ensure consistent text size on mobile */
  [lang="ru"] .text-xs,
  [lang="ru"] .text-sm,
  [lang="ru"] .text-base,
  [lang="ru"] .text-lg,
  [lang="kk"] .text-xs,
  [lang="kk"] .text-sm,
  [lang="kk"] .text-base,
  [lang="kk"] .text-lg {
    font-size: 0.95em;
  }
}
