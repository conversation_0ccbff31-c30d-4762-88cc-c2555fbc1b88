/* Table styles */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* Table row hover effect */
tbody tr {
  transition: all 0.2s ease;
}

tbody tr:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Avatar styles */
.rounded-full {
  transition: transform 0.3s ease;
}

tr:hover .rounded-full {
  transform: scale(1.1);
}

/* Role badge animation */
span.rounded-full {
  transition: all 0.3s ease;
}

tr:hover span.rounded-full {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
}

/* Filter buttons hover effect */
button.rounded-full {
  transition: all 0.2s ease;
}

button.rounded-full:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Search input focus effect */
input.rounded-lg:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Table header sticky on scroll */
thead {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Responsive table */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

/* Ensure table takes full width on small screens */
@media (max-width: 640px) {
  .overflow-x-auto {
    margin: 0 -1rem;
    width: calc(100% + 2rem);
  }
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}