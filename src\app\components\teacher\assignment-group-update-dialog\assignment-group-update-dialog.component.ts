import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-assignment-group-update-dialog',
  templateUrl: './assignment-group-update-dialog.component.html',
  styleUrl: './assignment-group-update-dialog.component.css'
})
export class AssignmentGroupUpdateDialogComponent implements OnInit {
  assignmentGroupForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<AssignmentGroupUpdateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { group: any, threadId: number }
  ) {
    this.assignmentGroupForm = this.fb.group({
      name: ['', Validators.required],
      group_type: ['', Validators.required],
      weight: [
        null,
        [Validators.required, Validators.min(0.001), Validators.max(0.9)]
      ],
    });
  }

  ngOnInit(): void {
    // Initialize form with existing group data
    this.assignmentGroupForm.patchValue({
      name: this.data.group.name,
      group_type: this.data.group.group_type,
      weight: this.data.group.weight
    });
  }

  submit() {
    if (this.assignmentGroupForm.invalid) return;

    const formData = this.assignmentGroupForm.value;
    formData.weight = parseFloat(formData.weight);  // Convert to number

    this.dialogRef.close({
      ...formData,
      id: this.data.group.id,
      thread_id: this.data.threadId
    });
  }

  cancel() {
    this.dialogRef.close(); // Cancel
  }
}
