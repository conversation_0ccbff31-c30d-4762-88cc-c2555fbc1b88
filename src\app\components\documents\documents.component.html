<app-breadcrumbs [items]="breadcrumbs" />

<div class="container">
  <!-- Header with search -->
  <div
    class="flex flex-col md:flex-row justify-between items-start md:items-center mb-4"
  >
    <div class="font-medium mb-2 text-xl">
      {{ "DOCUMENTS.TITLE" | translate }}
    </div>

    <!-- Search bar -->
    <div class="relative w-full md:w-64">
      <input
        type="text"
        [(ngModel)]="searchQuery"
        (input)="onSearchInput($event)"
        placeholder="{{ 'DOCUMENTS.SEARCH' | translate }}"
        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
      <button
        *ngIf="isSearching"
        (click)="clearSearch()"
        class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>
  </div>

  <!-- Search Status Message -->
  <div
    *ngIf="isSearching"
    class="mt-2 text-xs text-gray-500 flex items-center mb-4"
  >
    <span
      >{{ "DOCUMENTS.SEARCH_RESULTS" | translate }}: "{{ searchQuery }}"</span
    >
    <button (click)="clearSearch()" class="ml-2 text-blue-500 hover:underline">
      {{ "DOCUMENTS.CLEAR_SEARCH" | translate }}
    </button>
  </div>

  <div class="flex flex-col md:flex-row gap-6">
    <!-- Categories sidebar -->
    <div class="w-full md:w-64 mb-4 md:mb-0">
      <div class="card p-4">
        <h3 class="font-medium text-sm mb-3">
          {{ "DOCUMENTS.CATEGORIES" | translate }}
        </h3>
        <ul class="space-y-2">
          <li *ngFor="let category of categories">
            <button
              (click)="selectCategory(category.id)"
              class="w-full text-left px-3 py-2 rounded-lg text-sm transition-colors"
              [ngClass]="
                selectedCategory === category.id
                  ? 'bg-blue-50 text-blue-600 font-medium'
                  : 'hover:bg-gray-100'
              "
            >
              {{ category.name }}
            </button>
          </li>
        </ul>
      </div>
    </div>

    <!-- Documents list -->
    <div class="flex-1">
      <div class="card">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {{ "DOCUMENTS.NAME" | translate }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {{ "DOCUMENTS.SIZE" | translate }}
                </th>
                <th
                  scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {{ "DOCUMENTS.ACTIONS" | translate }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                *ngFor="let document of filteredDocuments"
                class="hover:bg-gray-50"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div
                      class="flex-shrink-0 h-10 w-10 flex items-center justify-center"
                    >
                      <img
                        [src]="'/assets/icons/' + document.icon"
                        alt="File icon"
                        class="h-6 w-6"
                      />
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ document.name }}
                      </div>
                      <div class="text-xs text-gray-500">
                        {{ document.description }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatFileSize(document.size) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a
                    href="/assets/docs/example.pdf"
                    download
                    class="text-blue-600 hover:text-blue-900 flex items-center"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 mr-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                        clip-rule="evenodd"
                      />
                    </svg>
                    {{ "DOCUMENTS.DOWNLOAD" | translate }}
                  </a>
                </td>
              </tr>

              <!-- Empty state -->
              <tr *ngIf="filteredDocuments.length === 0">
                <td
                  colspan="4"
                  class="px-6 py-10 text-center text-sm text-gray-500"
                >
                  <div class="flex flex-col items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-10 w-10 text-gray-400 mb-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    <p>{{ "DOCUMENTS.NO_DOCUMENTS" | translate }}</p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
