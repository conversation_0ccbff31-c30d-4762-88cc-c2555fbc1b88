user  nginx;
worker_processes  auto;
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    sendfile      on;
    keepalive_timeout  65;

    server {
        listen 80;
        server_name 213.109.146.170;

        # Указываем корневой каталог
        root /usr/share/nginx/html;

        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
