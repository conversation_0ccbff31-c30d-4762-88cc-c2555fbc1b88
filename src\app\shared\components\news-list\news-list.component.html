<div class="grid grid-cols-1">

  <div *ngIf="displayedNews.length > 0" class="h-[400px] group">
    <a [routerLink]="['/news', displayedNews[0].id]">
      <div class="relative h-full rounded-md overflow-hidden">
        <!-- фон-картинка -->
        <div
          [ngStyle]="{ 'background-image': 'url(' + displayedNews[0].imageUrl + ')' }"
          class="absolute inset-0 bg-cover bg-center transition-transform duration-500 ease-in-out group-hover:scale-110"
        ></div>

        <!-- затемняющий оверлей -->
        <div class="absolute inset-0 bg-gradient-to-t from-black/80 to-black/10 z-0"></div>

        <!-- текст -->
        <div class="absolute bottom-6 left-6 z-20">
          <div class="text-2xl font-medium mb-2 text-white">
            {{ displayedNews[0].title }}
          </div>
          <div class="mb-2 text-sm text-gray-200">
            {{ displayedNews[0].content }}
          </div>
          <div class="text-xs text-white">Сегодня, 15:08</div>
        </div>
      </div>
    </a>

  </div>

</div>

<!-- Остальные новости -->
<div class="mt-5 grid 2xl:grid-cols-3 xl:grid-cols-2 grid-cols-1 gap-2 gap-y-4">
  <div
    *ngFor="let post of displayedNews; let i = index"
    class="p-1 rounded-2xl  dark:bg-gray-800 group flex flex-col h-[280px]"
    [hidden]="i === 0"
  >

    <!-- Картинка -->
    <div class="rounded-xl mb-2 h-[180px] overflow-hidden">
      <a [routerLink]="['/news', post.id]">
        <img
          [src]="post.imageUrl"
          alt=""
          class="h-full w-full object-cover rounded-md transition-transform duration-500 group-hover:scale-110"
        />
      </a>
    </div>
    <div class="flex">
      <div class=" text-xs text-gray-500">
        {{ post.pub_date }}
      </div>
      <div>

      </div>
    </div>

    <div class="text-base font-medium mb-1">
      {{ post.title | slice:0:50 }}..
    </div>

    <!-- Контент и дата -->
    <a href="" >
      <div>
        <!-- Контент (если showContent = true) -->
        <ng-container *ngIf="showContent">
          <div class="text-xs dark:text-gray-300 ">
            {{ post.content | slice:0:100 }}...
          </div>
        </ng-container>
      </div>
    </a>

  </div>
</div>


