<div class="p-4">
  <!-- Date selection form -->
  <div class="mb-6 flex flex-col md:flex-row gap-4 items-start md:items-center">
    <form [formGroup]="dateForm" class="flex flex-col md:flex-row gap-4 items-start md:items-center">
      <div class="flex flex-col">
        <label for="attendanceDate" class="text-sm font-medium text-gray-700 mb-1">{{ 'ATTENDANCE.ATTENDANCE_DATE' | translate }}</label>
        <input
          type="date"
          id="attendanceDate"
          formControlName="attendanceDate"
          class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          [value]="dateForm.get('attendanceDate')?.value"
        >
      </div>
    </form>

    <button
      (click)="loadAttendance()"
      class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
      [disabled]="isLoadingAttendance"
    >
      <span *ngIf="isLoadingAttendance">{{ 'ATTENDANCE.LOADING' | translate }}</span>
      <span *ngIf="!isLoadingAttendance">{{ 'ATTENDANCE.LOAD' | translate }}</span>
    </button>

    <button
      (click)="saveAllAttendance()"
      class="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 transition-colors ml-auto"
      [disabled]="isSaving"
    >
      <span *ngIf="isSaving">{{ 'ATTENDANCE.SAVING' | translate }}</span>
      <span *ngIf="!isSaving">{{ 'ATTENDANCE.SAVE_ALL' | translate }}</span>
    </button>
  </div>

  <!-- Error message -->
  <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
    {{ errorMessage }}
  </div>

  <!-- Loading indicator -->
  <div *ngIf="isLoadingStudents" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    <span class="ml-2">{{ 'ATTENDANCE.LOADING' | translate }}</span>
  </div>

  <!-- No students message -->
  <div *ngIf="!isLoadingStudents && students.length === 0" class="text-center py-8 text-gray-500">
    {{ 'ATTENDANCE.NO_STUDENTS' | translate }}
  </div>

  <!-- Attendance table -->
  <div *ngIf="!isLoadingStudents && students.length > 0" class="relative overflow-x-auto border sm:rounded-lg">
    <table class="w-full text-sm text-left text-gray-500">
      <thead class="text-xs text-blue-400 capitalize bg-gray-100">
        <tr>
          <th class="px-2 py-3 w-12">№</th>
          <th class="px-6 py-3">{{ 'ATTENDANCE.STUDENT' | translate }}</th>
          <th class="px-6 py-3">{{ 'ATTENDANCE.STATUS' | translate }}</th>
          <th class="px-6 py-3">{{ 'ATTENDANCE.REASON' | translate }}</th>
          <th class="px-6 py-3">{{ 'ATTENDANCE.ACTIONS' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let student of students; let i = index"
            class="odd:bg-white odd:dark:bg-gray-900 even:bg-gray-50 even:dark:bg-gray-800 border-b">

          <!-- Number -->
          <td class="px-2 py-4 text-black">
            {{ i + 1 }}
          </td>

          <!-- Student name -->
          <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
            {{ student.name }} {{ student.surname }}
          </td>

          <!-- Status -->
          <td class="px-6 py-4">
            <span
              [ngClass]="{
                'bg-gray-200 text-gray-800': student.attendance?.status === attendanceStatus.UNMARKED || student.attendance?.status === attendanceStatus.UNSPECIFIED,
                'bg-green-100 text-green-800': student.attendance?.status === attendanceStatus.PRESENT,
                'bg-red-100 text-red-800': student.attendance?.status === attendanceStatus.ABSENT,
                'bg-yellow-100 text-yellow-800': student.attendance?.status === attendanceStatus.EXCUSED
              }"
              class="px-2 py-1 rounded-full text-xs font-medium"
            >
              {{ getStatusLabel(student.attendance?.status || attendanceStatus.UNMARKED) | translate }}
            </span>
          </td>

          <!-- Reason (for excused absences) -->
          <td class="px-6 py-4">
            <div *ngIf="student.attendance?.status === attendanceStatus.EXCUSED">
              <form [formGroup]="reasonForms[student.id]">
                <input
                  type="text"
                  formControlName="reason"
                  class="border border-gray-300 rounded-md px-3 py-1 text-sm w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
                  [placeholder]="'ATTENDANCE.REASON_PLACEHOLDER' | translate"
                >
              </form>
            </div>
            <div *ngIf="student.attendance?.status !== attendanceStatus.EXCUSED && student.attendance?.reason" class="text-gray-500 text-xs">
              {{ student.attendance?.reason }}
            </div>
          </td>

          <!-- Actions -->
          <td class="px-6 py-4">
            <div class="flex space-x-2">
              <button
                (click)="updateAttendance(student, attendanceStatus.PRESENT)"
                class="px-3 py-1 bg-green-600 text-white rounded-md text-xs hover:bg-green-700 transition-colors"
                [disabled]="isSaving"
              >
                {{ 'ATTENDANCE.PRESENT' | translate }}
              </button>
              <button
                (click)="updateAttendance(student, attendanceStatus.ABSENT)"
                class="px-3 py-1 bg-red-600 text-white rounded-md text-xs hover:bg-red-700 transition-colors"
                [disabled]="isSaving"
              >
                {{ 'ATTENDANCE.ABSENT' | translate }}
              </button>
              <button
                (click)="updateAttendance(student, attendanceStatus.EXCUSED)"
                class="px-3 py-1 bg-yellow-600 text-white rounded-md text-xs hover:bg-yellow-700 transition-colors"
                [disabled]="isSaving"
              >
                {{ 'ATTENDANCE.EXCUSED' | translate }}
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
