/* Custom styles for the calendar component */
.card {
  background-color: var(--bg-card);
  border: 1px solid var(--border-light);
}

/* Override Angular Material calendar styles */
::ng-deep .mat-calendar {
  font-family: inherit !important;
}

::ng-deep .mat-calendar-body-selected {
  background-color: #3b82f6 !important;
  color: white !important;
}

::ng-deep .mat-calendar-body-today:not(.mat-calendar-body-selected) {
  border-color: #3b82f6 !important;
}

::ng-deep .mat-calendar-arrow {
  border-top-color: #3b82f6 !important;
}

::ng-deep .mat-calendar-body-cell-content {
  font-size: 12px !important;
}

::ng-deep .mat-calendar-table-header th {
  font-size: 11px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr !important;
  }
}