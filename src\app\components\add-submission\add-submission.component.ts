import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { environments } from "../../environments/environments";
import { EditorComponent } from "@tinymce/tinymce-angular";
import { SubmissionService } from "../../core/services/submission.service";
import { AuthService } from "../../core/services/auth.service";
import { MatSnackBar } from "@angular/material/snack-bar";

@Component({
  selector: 'app-add-submission',
  templateUrl: './add-submission.component.html',
  styleUrl: './add-submission.component.css'
})
export class AddSubmissionComponent implements OnInit {
  @Input() assignmentId!: number;
  @Input() assignment: any; // To check deadline
  @Output() submissionComplete = new EventEmitter<void>();

  editor_key = environments.TINY_MC_KEY;
  init: EditorComponent['init'] = {
    plugins: 'lists link wordcount',
    toolbar: 'undo redo | bold italic underline | bullist numlist | link | wordcount',
    height: 250,
    menubar: false,
    branding: false
  };

  comment: string = '';
  selectedFiles: File[] = [];
  uploadedFileUrls: string[] = [];
  isSubmitting: boolean = false;
  isDeadlinePassed: boolean = false;

  constructor(
    private submissionService: SubmissionService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Check if deadline has passed
    if (this.assignment && this.assignment.due_date) {
      this.checkDeadline();
    }
  }

  /**
   * Check if the assignment deadline has passed
   */
  private checkDeadline(): void {
    if (!this.assignment || !this.assignment.due_date) {
      this.isDeadlinePassed = false;
      return;
    }

    let dueDate: Date;

    // Handle different date formats
    if (this.assignment.due_date.seconds) {
      // Firestore timestamp format
      dueDate = new Date(this.assignment.due_date.seconds * 1000);
    } else if (typeof this.assignment.due_date === 'string') {
      // ISO string format
      dueDate = new Date(this.assignment.due_date);
    } else {
      // Unknown format
      this.isDeadlinePassed = false;
      return;
    }

    const now = new Date();
    this.isDeadlinePassed = now > dueDate;
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files) {
      const file = input.files[0];
      if (file) {
        this.selectedFiles.push(file);
      }
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
  }

  onEditorChange(event: any): void {
    this.comment = event.editor.getContent();
  }

  async submitAssignment(): Promise<void> {
    if (!this.assignmentId) {
      this.snackBar.open('Ошибка: ID задания не найден', 'Закрыть', {
        duration: 3000,
        panelClass: ['snackbar-error']
      });
      return;
    }

    // Check if deadline has passed
    if (this.assignment && this.assignment.due_date) {
      this.checkDeadline();
      if (this.isDeadlinePassed) {
        this.snackBar.open('Срок сдачи задания истек', 'Закрыть', {
          duration: 3000,
          panelClass: ['snackbar-error']
        });
        return;
      }
    }

    if (this.selectedFiles.length === 0 && !this.comment) {
      this.snackBar.open('Пожалуйста, добавьте файл или комментарий', 'Закрыть', {
        duration: 3000,
        panelClass: ['snackbar-error']
      });
      return;
    }

    this.isSubmitting = true;

    try {
      // Use the new direct file upload method if there's only one file
      if (this.selectedFiles.length === 1) {
        // Submit the assignment with direct file upload
        this.submissionService.submitAssignmentWithFile(
          this.assignmentId,
          this.selectedFiles[0],
          this.comment
        ).subscribe({
          next: (response) => {
            this.snackBar.open('Задание успешно отправлено', 'Закрыть', {
              duration: 3000,
              panelClass: ['snackbar-success']
            });
            this.resetForm();
            this.isSubmitting = false;
            // Emit event to parent component to refresh submissions
            this.submissionComplete.emit();
          },
          error: (error) => {
            console.error('Error submitting assignment:', error);
            this.snackBar.open(
              error.error?.error || 'Ошибка при отправке задания',
              'Закрыть',
              {
                duration: 5000,
                panelClass: ['snackbar-error']
              }
            );
            this.isSubmitting = false;
          }
        });
      } else {
        // For multiple files or no files, use the legacy method
        // Upload files first if any
        if (this.selectedFiles.length > 0) {
          for (const file of this.selectedFiles) {
            await this.uploadFile(file);
          }
        }

        // Submit the assignment with the uploaded file URLs
        this.submissionService.submitAssignment(
          this.assignmentId,
          this.uploadedFileUrls,
          this.comment
        ).subscribe({
          next: (response) => {
            this.snackBar.open('Задание успешно отправлено', 'Закрыть', {
              duration: 3000,
              panelClass: ['snackbar-success']
            });
            this.resetForm();
            this.isSubmitting = false;
            // Emit event to parent component to refresh submissions
            this.submissionComplete.emit();
          },
          error: (error) => {
            console.error('Error submitting assignment:', error);
            this.snackBar.open(
              error.error?.error || 'Ошибка при отправке задания',
              'Закрыть',
              {
                duration: 5000,
                panelClass: ['snackbar-error']
              }
            );
            this.isSubmitting = false;
          }
        });
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      this.snackBar.open('Ошибка при загрузке файлов', 'Закрыть', {
        duration: 3000,
        panelClass: ['snackbar-error']
      });
      this.isSubmitting = false;
    }
  }

  private async uploadFile(file: File): Promise<void> {
    return new Promise((resolve, reject) => {
      this.submissionService.uploadFile(file).subscribe({
        next: (response) => {
          if (response && response.url) {
            this.uploadedFileUrls.push(response.url);
            resolve();
          } else {
            reject('Invalid response from server');
          }
        },
        error: (error) => {
          reject(error);
        }
      });
    });
  }

  private resetForm(): void {
    this.comment = '';
    this.selectedFiles = [];
    this.uploadedFileUrls = [];
  }
}
