<div class="flex min-h-screen bg-secondary">
  <app-side-bar [collapsed]="sidebarCollapsed" (toggleSidebarEvent)="toggleSidebar()"></app-side-bar>

  <div class="flex-1 transition-all duration-300" [ngClass]="{'ml-20': sidebarCollapsed, 'ml-0 md:ml-64': !sidebarCollapsed}">
    <app-header
      (toggleSidebarEvent)="toggleSidebar()"
      (toggleNotificationsEvent)="toggleNotifications()">
    </app-header>

    <div class="flex relative">
      <main class="flex-1 px-3 sm:px-5 mt-2 w-full">
        <router-outlet></router-outlet>
      </main>

      <aside *ngIf="showNotifications" class="fixed right-0 top-16 w-80 border-l h-[calc(100vh-64px)] card overflow-auto z-20 md:relative md:top-0">
        <div class="p-4 text-xs">
          <div class="font-medium mb-4">{{ 'NOTIFICATIONS.TITLE' | translate }}</div>

          <!-- Empty state - No notifications -->
          <div class="py-8 text-center">
            <div class="bg-gray-100 dark:bg-gray-800 rounded-full p-3 mx-auto w-14 h-14 flex items-center justify-center mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
            </div>
            <p class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ 'NOTIFICATIONS.NO_NOTIFICATIONS' | translate }}</p>
            <p class="text-xs text-gray-500 mt-1">{{ 'NOTIFICATIONS.WILL_APPEAR_LATER' | translate }}</p>
          </div>
        </div>
      </aside>
    </div>
  </div>
</div>

